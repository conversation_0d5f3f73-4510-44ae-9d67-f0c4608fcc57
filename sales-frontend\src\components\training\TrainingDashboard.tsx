import React, { useState, useEffect } from 'react';
import { VideoProgress, videosAPI } from '../../services/api';
import VideoPlayer from './VideoPlayer';
import {
  PlayIcon,
  CheckCircleIcon,
  ClockIcon,
  AcademicCapIcon,
  LockClosedIcon,
} from '@heroicons/react/24/outline';

const TrainingDashboard: React.FC = () => {
  const [progress, setProgress] = useState<VideoProgress[]>([]);
  const [trainingStatus, setTrainingStatus] = useState<{ trainingCompleted: boolean } | null>(null);
  const [selectedVideo, setSelectedVideo] = useState<VideoProgress | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      console.log('Fetching training data...');
      const [progressData, statusData] = await Promise.all([
        videosAPI.getMyProgress(),
        videosAPI.getTrainingStatus(),
      ]);
      console.log('Progress data:', progressData);
      console.log('Status data:', statusData);

      // Debug each video in the progress data
      progressData.forEach((progress, index) => {
        console.log(`Video ${index + 1}:`, {
          progressId: progress.id,
          videoId: progress.videoId,
          isCompleted: progress.isCompleted,
          video: progress.video
        });

        if (progress.video) {
          console.log(`  Video details:`, {
            id: progress.video.id,
            title: progress.video.title,
            filename: progress.video?.filename,
            streamUrl: videosAPI.getStreamUrl(progress.video.id)
          });
        }
      });

      setProgress(progressData);
      setTrainingStatus(statusData);
    } catch (err: any) {
      console.error('Error fetching training data:', err);
      setError(err.response?.data?.message || 'Failed to fetch training data');
    } finally {
      setLoading(false);
    }
  };

  const handleVideoComplete = async (videoId: number) => {
    try {
      await videosAPI.updateProgress(videoId, true);
      await fetchData(); // Refresh data to update completion status
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update progress');
    }
  };

  const completedVideos = progress.filter(p => p.isCompleted).length;
  const totalVideos = progress.length;
  const completionPercentage = totalVideos > 0 ? (completedVideos / totalVideos) * 100 : 0;

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-semibold text-gray-900">Training Center</h1>
        <p className="mt-2 text-sm text-gray-700">
          Complete all required training videos to access the sales platform.
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* No videos message */}
      {!loading && progress.length === 0 && !error && (
        <div className="mb-6 p-8 bg-yellow-50 border border-yellow-200 rounded-md text-center">
          <AcademicCapIcon className="mx-auto h-12 w-12 text-yellow-400 mb-4" />
          <h3 className="text-lg font-medium text-yellow-800 mb-2">No Training Videos Available</h3>
          <p className="text-yellow-700">
            No training videos have been uploaded yet. Please contact your administrator.
          </p>
        </div>
      )}



      {/* Training Status Card */}
      <div className="mb-8 bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`flex-shrink-0 h-12 w-12 rounded-full flex items-center justify-center ${
              trainingStatus?.trainingCompleted ? 'bg-green-100' : 'bg-yellow-100'
            }`}>
              {trainingStatus?.trainingCompleted ? (
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
              ) : (
                <ClockIcon className="h-8 w-8 text-yellow-600" />
              )}
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">
                Training Status
              </h3>
              <p className={`text-sm ${
                trainingStatus?.trainingCompleted ? 'text-green-600' : 'text-yellow-600'
              }`}>
                {trainingStatus?.trainingCompleted 
                  ? 'Training Completed - You can now access all platform features!'
                  : 'Training In Progress - Complete all videos to unlock the platform'
                }
              </p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {completedVideos}/{totalVideos}
            </div>
            <div className="text-sm text-gray-500">Videos Completed</div>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-4">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>Progress</span>
            <span>{completionPercentage.toFixed(0)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className={`h-3 rounded-full transition-all duration-300 ${
                trainingStatus?.trainingCompleted ? 'bg-green-600' : 'bg-primary-600'
              }`}
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Video List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <AcademicCapIcon className="h-5 w-5 mr-2" />
            Training Videos
          </h3>
        </div>
        <div className="divide-y divide-gray-200">
          {progress.map((videoProgress, index) => {
            const video = videoProgress.video;
            if (!video) return null;

            const isLocked = index > 0 && !progress[index - 1]?.isCompleted;

            return (
              <div key={video.id} className={`p-6 ${isLocked ? 'opacity-50' : ''}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`flex-shrink-0 h-12 w-12 rounded-full flex items-center justify-center ${
                      videoProgress.isCompleted 
                        ? 'bg-green-100' 
                        : isLocked 
                        ? 'bg-gray-100' 
                        : 'bg-primary-100'
                    }`}>
                      {videoProgress.isCompleted ? (
                        <CheckCircleIcon className="h-6 w-6 text-green-600" />
                      ) : isLocked ? (
                        <LockClosedIcon className="h-6 w-6 text-gray-400" />
                      ) : (
                        <PlayIcon className="h-6 w-6 text-primary-600" />
                      )}
                    </div>
                    <div>
                      <h4 className="text-lg font-medium text-gray-900">{video.title}</h4>
                      {video.description && (
                        <p className="text-sm text-gray-500 mt-1">{video.description}</p>
                      )}
                      <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                        <span>Video {index + 1} of {totalVideos}</span>
                        {video.duration && (
                          <span>{Math.ceil(video.duration / 60)} minutes</span>
                        )}
                        {video.isRequired && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            Required
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    {videoProgress.isCompleted && (
                      <span className="text-sm text-green-600 font-medium">
                        Completed {videoProgress.watchedAt && new Date(videoProgress.watchedAt).toLocaleDateString()}
                      </span>
                    )}
                    <button
                      onClick={() => setSelectedVideo(videoProgress)}
                      disabled={isLocked}
                      className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                        isLocked
                          ? 'bg-gray-400 cursor-not-allowed'
                          : videoProgress.isCompleted
                          ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                          : 'bg-primary-600 hover:bg-primary-700 focus:ring-primary-500'
                      }`}
                    >
                      <PlayIcon className="h-4 w-4 mr-2" />
                      {videoProgress.isCompleted ? 'Watch Again' : 'Watch Video'}
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Video Player Modal */}
      {selectedVideo && (
        <VideoPlayer
          videoProgress={selectedVideo}
          onComplete={handleVideoComplete}
          onClose={() => setSelectedVideo(null)}
        />
      )}
    </div>
  );
};

export default TrainingDashboard;

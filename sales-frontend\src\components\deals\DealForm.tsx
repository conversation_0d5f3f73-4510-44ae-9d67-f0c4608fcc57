import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { Deal, Client, dealsAPI, clientsAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import {
  ArrowLeftIcon,
  CurrencyDollarIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  BanknotesIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';

interface DealFormProps {
  isEdit?: boolean;
}

const DealForm: React.FC<DealFormProps> = ({ isEdit = false }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const location = useLocation();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [loadingClients, setLoadingClients] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [clients, setClients] = useState<Client[]>([]);

  // Check if we're in admin context
  const isAdminContext = location.pathname.startsWith('/admin');

  // Helper function to generate correct paths
  const getDealPath = (path: string) => {
    return isAdminContext ? `/admin${path}` : path;
  };
  const [formData, setFormData] = useState({
    clientId: '',
    amount: '',
    currency: 'USD',
    description: '',
    details: '',
    notes: '',
  });

  const currencies = [
    { value: 'USD', label: 'USD - US Dollar', symbol: '$' },
    { value: 'EUR', label: 'EUR - Euro', symbol: '€' },
    { value: 'GBP', label: 'GBP - British Pound', symbol: '£' },
    { value: 'CAD', label: 'CAD - Canadian Dollar', symbol: 'C$' },
    { value: 'AUD', label: 'AUD - Australian Dollar', symbol: 'A$' },
    { value: 'JPY', label: 'JPY - Japanese Yen', symbol: '¥' },
  ];

  useEffect(() => {
    fetchClients();
    if (isEdit && id) {
      fetchDeal();
    }
  }, [isEdit, id]);

  const fetchClients = async () => {
    try {
      setLoadingClients(true);
      const data = await clientsAPI.getAll();
      setClients(data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch clients');
    } finally {
      setLoadingClients(false);
    }
  };

  const fetchDeal = async () => {
    try {
      setLoading(true);
      const deal = await dealsAPI.getById(Number(id));
      setFormData({
        clientId: deal.clientId.toString(),
        amount: deal.amount,
        currency: deal.currency,
        description: deal.description,
        details: deal.details || '',
        notes: deal.notes || '',
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch deal');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // Clear field error when user starts typing
    if (fieldErrors[name]) {
      setFieldErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    if (!formData.clientId) {
      errors.clientId = 'Please select a client';
    }
    
    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      errors.amount = 'Please enter a valid amount greater than 0';
    }
    
    if (!formData.description.trim()) {
      errors.description = 'Deal title is required';
    }
    
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    
    if (!validateForm()) {
      return;
    }
    
    setSaving(true);

    try {
      const submitData = {
        clientId: Number(formData.clientId),
        amount: parseFloat(formData.amount),
        currency: formData.currency,
        description: formData.description,
        details: formData.details || undefined,
        notes: formData.notes || undefined,
      };

      if (isEdit && id) {
        await dealsAPI.update(Number(id), submitData);
        setSuccess('Deal updated successfully!');
      } else {
        await dealsAPI.create(submitData);
        setSuccess('Deal created successfully!');
      }
      
      setTimeout(() => navigate(getDealPath('/deals')), 1500);
    } catch (err: any) {
      setError(err.response?.data?.message || `Failed to ${isEdit ? 'update' : 'create'} deal`);
    } finally {
      setSaving(false);
    }
  };

  const getCurrencySymbol = (currency: string) => {
    return currencies.find(c => c.value === currency)?.symbol || '$';
  };

  const formatAmount = (amount: string) => {
    if (!amount) return '';
    const num = parseFloat(amount);
    if (isNaN(num)) return amount;
    return num.toLocaleString();
  };

  const selectedClient = clients.find(c => c.id === Number(formData.clientId));

  if (loadingClients) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-sm text-gray-600">Loading clients...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate(getDealPath('/deals'))}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4 transition-colors"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Deals
          </button>
          
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <div className="h-16 w-16 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center shadow-sm">
                <CurrencyDollarIcon className="h-8 w-8 text-green-600" />
              </div>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {isEdit ? 'Edit Deal' : 'Create New Deal'}
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                {isEdit ? 'Update deal information and details' : 'Add a new deal to track your sales progress'}
              </p>
            </div>
          </div>
        </div>

        {/* Success Message */}
        {success && (
          <div className="mb-6 rounded-lg bg-green-50 p-4 border border-green-200 shadow-sm">
            <div className="flex">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">{success}</p>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="mb-6 rounded-lg bg-red-50 p-4 border border-red-200 shadow-sm">
            <div className="flex">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Client Selection Card */}
          <div className="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl">
            <div className="px-4 py-6 sm:p-8">
              <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                <div className="sm:col-span-6">
                  <h3 className="text-lg font-semibold leading-7 text-gray-900 flex items-center">
                    <BuildingOfficeIcon className="h-5 w-5 mr-2 text-gray-400" />
                    Client Information
                  </h3>
                  <p className="mt-1 text-sm leading-6 text-gray-600">
                    Select the client for this deal
                  </p>
                </div>

                {/* Client Selection */}
                <div className="sm:col-span-6">
                  <label htmlFor="clientId" className="block text-sm font-medium leading-6 text-gray-900">
                    Client *
                  </label>
                  <div className="mt-2">
                    <select
                      id="clientId"
                      name="clientId"
                      required
                      className={`block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 transition-colors ${
                        fieldErrors.clientId 
                          ? 'ring-red-300 focus:ring-red-500' 
                          : 'ring-gray-300 focus:ring-primary-600'
                      }`}
                      value={formData.clientId}
                      onChange={handleChange}
                    >
                      <option value="">Select a client</option>
                      {clients.map((client) => (
                        <option key={client.id} value={client.id}>
                          {client.name} ({client.type})
                        </option>
                      ))}
                    </select>
                    {fieldErrors.clientId && (
                      <p className="mt-2 text-sm text-red-600 flex items-center">
                        <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                        {fieldErrors.clientId}
                      </p>
                    )}
                  </div>

                  {/* Selected Client Info */}
                  {selectedClient && (
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <BuildingOfficeIcon className="h-8 w-8 text-gray-400" />
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">{selectedClient.name}</h4>
                          <p className="text-sm text-gray-500">{selectedClient.type}</p>
                          {selectedClient.email && (
                            <p className="text-sm text-gray-500">{selectedClient.email}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Deal Details Card */}
          <div className="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl">
            <div className="px-4 py-6 sm:p-8">
              <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                <div className="sm:col-span-6">
                  <h3 className="text-lg font-semibold leading-7 text-gray-900 flex items-center">
                    <BanknotesIcon className="h-5 w-5 mr-2 text-gray-400" />
                    Deal Details
                  </h3>
                  <p className="mt-1 text-sm leading-6 text-gray-600">
                    Financial information and deal specifics
                  </p>
                </div>

                {/* Amount and Currency */}
                <div className="sm:col-span-4">
                  <label htmlFor="amount" className="block text-sm font-medium leading-6 text-gray-900">
                    Deal Amount *
                  </label>
                  <div className="mt-2">
                    <div className="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-600">
                      <span className="flex select-none items-center pl-3 text-gray-500 sm:text-sm font-medium">
                        {getCurrencySymbol(formData.currency)}
                      </span>
                      <input
                        type="number"
                        name="amount"
                        id="amount"
                        step="0.01"
                        min="0"
                        required
                        className={`block flex-1 border-0 bg-transparent py-2.5 pl-2 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6 ${
                          fieldErrors.amount ? 'text-red-900' : ''
                        }`}
                        value={formData.amount}
                        onChange={handleChange}
                        placeholder="0.00"
                      />
                    </div>
                    {fieldErrors.amount && (
                      <p className="mt-2 text-sm text-red-600 flex items-center">
                        <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                        {fieldErrors.amount}
                      </p>
                    )}
                    {formData.amount && !fieldErrors.amount && (
                      <p className="mt-2 text-sm text-gray-600">
                        Formatted: {getCurrencySymbol(formData.currency)}{formatAmount(formData.amount)}
                      </p>
                    )}
                  </div>
                </div>

                {/* Currency */}
                <div className="sm:col-span-2">
                  <label htmlFor="currency" className="block text-sm font-medium leading-6 text-gray-900">
                    Currency
                  </label>
                  <div className="mt-2">
                    <select
                      name="currency"
                      id="currency"
                      className="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6"
                      value={formData.currency}
                      onChange={handleChange}
                    >
                      {currencies.map((currency) => (
                        <option key={currency.value} value={currency.value}>
                          {currency.value}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="mt-2">
                    <span className="text-xs text-gray-500">
                      {currencies.find(c => c.value === formData.currency)?.label}
                    </span>
                  </div>
                </div>

                {/* Description */}
                <div className="sm:col-span-6">
                  <label htmlFor="description" className="block text-sm font-medium leading-6 text-gray-900">
                    Deal Title *
                  </label>
                  <div className="mt-2">
                    <div className="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-600">
                      <span className="flex select-none items-center pl-3 text-gray-500 sm:text-sm">
                        <DocumentTextIcon className="h-4 w-4" />
                      </span>
                      <input
                        type="text"
                        name="description"
                        id="description"
                        required
                        className={`block flex-1 border-0 bg-transparent py-2.5 pl-2 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6 ${
                          fieldErrors.description ? 'text-red-900' : ''
                        }`}
                        value={formData.description}
                        onChange={handleChange}
                        placeholder="Enter deal or order title (e.g., 'Software License Agreement', 'Q4 Marketing Campaign')"
                      />
                    </div>
                    {fieldErrors.description && (
                      <p className="mt-2 text-sm text-red-600 flex items-center">
                        <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                        {fieldErrors.description}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information Card */}
          <div className="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl">
            <div className="px-4 py-6 sm:p-8">
              <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                <div className="sm:col-span-6">
                  <h3 className="text-lg font-semibold leading-7 text-gray-900 flex items-center">
                    <ChatBubbleLeftRightIcon className="h-5 w-5 mr-2 text-gray-400" />
                    Additional Information
                  </h3>
                  <p className="mt-1 text-sm leading-6 text-gray-600">
                    Detailed information and notes about this deal
                  </p>
                </div>

                {/* Details */}
                <div className="sm:col-span-6">
                  <label htmlFor="details" className="block text-sm font-medium leading-6 text-gray-900">
                    Deal Details & Specifications
                  </label>
                  <p className="mt-1 text-sm text-gray-600">
                    Provide comprehensive information about the deal including specifications, requirements, terms, deliverables, and any other relevant details.
                  </p>
                  <div className="mt-2">
                    <textarea
                      name="details"
                      id="details"
                      rows={8}
                      className="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6"
                      value={formData.details}
                      onChange={handleChange}
                      placeholder="Enter detailed information about the deal:
• Product/service specifications
• Delivery requirements and timeline
• Terms and conditions
• Technical requirements
• Scope of work
• Special considerations
• Any other relevant details..."
                    />
                  </div>
                </div>

                {/* Notes */}
                <div className="sm:col-span-6">
                  <label htmlFor="notes" className="block text-sm font-medium leading-6 text-gray-900">
                    Notes
                  </label>
                  <div className="mt-2">
                    <textarea
                      name="notes"
                      id="notes"
                      rows={4}
                      className="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6"
                      value={formData.notes}
                      onChange={handleChange}
                      placeholder="Additional notes about the deal, terms, conditions, or important details..."
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Deal Summary Card */}
          {formData.clientId && formData.amount && formData.description && (
            <div className="bg-gradient-to-r from-primary-50 to-green-50 shadow-sm ring-1 ring-primary-200 sm:rounded-xl">
              <div className="px-4 py-6 sm:p-8">
                <h3 className="text-lg font-semibold leading-7 text-gray-900 flex items-center mb-4">
                  <CheckCircleIcon className="h-5 w-5 mr-2 text-green-500" />
                  Deal Summary
                </h3>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-600">Client</dt>
                    <dd className="text-sm text-gray-900 font-medium">
                      {selectedClient?.name}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-600">Amount</dt>
                    <dd className="text-lg font-bold text-green-600">
                      {getCurrencySymbol(formData.currency)}{formatAmount(formData.amount)}
                    </dd>
                  </div>
                  <div className="sm:col-span-2">
                    <dt className="text-sm font-medium text-gray-600">Description</dt>
                    <dd className="text-sm text-gray-900">{formData.description}</dd>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex items-center justify-end gap-x-6 px-4 py-4 sm:px-8">
            <button
              type="button"
              onClick={() => navigate(getDealPath('/deals'))}
              className="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving}
              className="rounded-md bg-primary-600 px-6 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {saving ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEdit ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                isEdit ? 'Update Deal' : 'Create Deal'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DealForm;

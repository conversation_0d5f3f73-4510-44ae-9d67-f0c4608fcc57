import { IsString, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON>y, IsInt } from 'class-validator';
import { ClientType, ClientImportance } from '@prisma/client';

export class CreateClientDto {
  @IsString()
  name: string;

  @IsEnum(ClientType)
  type: ClientType;

  @IsOptional()
  @IsString()
  email?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  phoneNumbers?: string[];

  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsEnum(ClientImportance)
  importance?: ClientImportance;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsInt()
  assignedUserId?: number;
}

import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateClientDto } from './dto/create-client.dto';
import { UpdateClientDto } from './dto/update-client.dto';
import { User } from '@prisma/client';

@Injectable()
export class ClientsService {
  constructor(private prisma: PrismaService) {}

  async create(createClientDto: CreateClientDto, user: User) {
    return this.prisma.client.create({
      data: {
        ...createClientDto,
        phoneNumbers: createClientDto.phoneNumbers || [],
        createdById: user.id, // Track who created the client
        // If user is not admin, assign the client to themselves
        assignedUserId: user.role === 'ADMIN' ? createClientDto.assignedUserId : user.id,
      },
      include: {
        assignedUser: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  async findAll(user: User) {
    const whereClause = user.role === 'ADMIN'
      ? {} // Admin sees all clients
      : {
          OR: [
            { assignedUserId: user.id }, // Clients assigned to the user
            { createdById: user.id }     // Clients created by the user
          ]
        };

    return this.prisma.client.findMany({
      where: whereClause,
      include: {
        assignedUser: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        _count: {
          select: {
            deals: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: number, user: User) {
    const client = await this.prisma.client.findUnique({
      where: { id },
      include: {
        assignedUser: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        deals: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    if (!client) {
      throw new NotFoundException(`Client with ID ${id} not found`);
    }

    // Check access permissions for non-admin users
    if (user.role !== 'ADMIN') {
      const hasAccess = client.assignedUserId === user.id || client.createdById === user.id;
      if (!hasAccess) {
        throw new NotFoundException(`Client with ID ${id} not found or access denied`);
      }
    }

    return client;
  }

  async update(id: number, updateClientDto: UpdateClientDto, user: User) {
    const client = await this.prisma.client.findUnique({
      where: { id },
    });

    if (!client) {
      throw new NotFoundException(`Client with ID ${id} not found`);
    }

    // Check access permissions for non-admin users
    if (user.role !== 'ADMIN') {
      const hasAccess = client.assignedUserId === user.id || client.createdById === user.id;
      if (!hasAccess) {
        throw new NotFoundException(`Client with ID ${id} not found or access denied`);
      }
    }

    // Prevent regular users from changing assignment or creation info
    const updateData = user.role === 'ADMIN'
      ? updateClientDto
      : {
          ...updateClientDto,
          assignedUserId: undefined,
          createdById: undefined
        };

    return this.prisma.client.update({
      where: { id },
      data: updateData,
      include: {
        assignedUser: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  async remove(id: number, user: User) {
    const client = await this.prisma.client.findUnique({
      where: { id },
    });

    if (!client) {
      throw new NotFoundException(`Client with ID ${id} not found`);
    }

    // Check access permissions for non-admin users
    if (user.role !== 'ADMIN') {
      const hasAccess = client.assignedUserId === user.id || client.createdById === user.id;
      if (!hasAccess) {
        throw new NotFoundException(`Client with ID ${id} not found or access denied`);
      }
    }

    return this.prisma.client.delete({
      where: { id },
    });
  }

  // New method for admin to assign clients to users
  async assignClient(clientId: number, userId: number, adminUser: User) {
    if (adminUser.role !== 'ADMIN') {
      throw new NotFoundException('Only admins can assign clients');
    }

    const client = await this.prisma.client.findUnique({
      where: { id: clientId },
    });

    if (!client) {
      throw new NotFoundException(`Client with ID ${clientId} not found`);
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId, role: 'USER' },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found or not a salesman`);
    }

    return this.prisma.client.update({
      where: { id: clientId },
      data: { assignedUserId: userId },
      include: {
        assignedUser: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  // New method for admin to unassign clients
  async unassignClient(clientId: number, adminUser: User) {
    if (adminUser.role !== 'ADMIN') {
      throw new NotFoundException('Only admins can unassign clients');
    }

    const client = await this.prisma.client.findUnique({
      where: { id: clientId },
    });

    if (!client) {
      throw new NotFoundException(`Client with ID ${clientId} not found`);
    }

    return this.prisma.client.update({
      where: { id: clientId },
      data: { assignedUserId: null },
      include: {
        assignedUser: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  async getClientDeals(id: number) {
    const client = await this.prisma.client.findUnique({
      where: { id },
    });

    if (!client) {
      throw new NotFoundException(`Client with ID ${id} not found`);
    }

    return this.prisma.deal.findMany({
      where: { clientId: id },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
}

import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Int, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateDealDto {
  @IsInt()
  clientId: number;

  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Type(() => Number)
  amount: number;

  @IsOptional()
  @IsString()
  currency?: string = 'USD';

  @IsString()
  description: string;

  @IsOptional()
  @IsString()
  details?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { videosAPI } from '../../services/api';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  AcademicCapIcon,
} from '@heroicons/react/24/outline';

interface TrainingStatusProps {
  compact?: boolean;
}

const TrainingStatus: React.FC<TrainingStatusProps> = ({ compact = false }) => {
  const [trainingCompleted, setTrainingCompleted] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkTrainingStatus();
  }, []);

  const checkTrainingStatus = async () => {
    try {
      const status = await videosAPI.getTrainingStatus();
      setTrainingCompleted(status.trainingCompleted);
    } catch (error) {
      console.error('Failed to check training status:', error);
      setTrainingCompleted(false);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return compact ? (
      <div className="animate-pulse h-4 w-16 bg-gray-200 rounded"></div>
    ) : (
      <div className="animate-pulse h-12 bg-gray-200 rounded-lg"></div>
    );
  }

  if (trainingCompleted) {
    return compact ? (
      <div className="flex items-center text-green-600">
        <CheckCircleIcon className="h-4 w-4 mr-1" />
        <span className="text-xs">Training Complete</span>
      </div>
    ) : (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center">
          <CheckCircleIcon className="h-5 w-5 text-green-600 mr-2" />
          <span className="text-sm font-medium text-green-800">
            Training completed! You have full access to the platform.
          </span>
        </div>
      </div>
    );
  }

  return compact ? (
    <Link 
      to="/training" 
      className="flex items-center text-yellow-600 hover:text-yellow-700"
    >
      <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
      <span className="text-xs">Complete Training</span>
    </Link>
  ) : (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2" />
          <div>
            <span className="text-sm font-medium text-yellow-800">
              Training Required
            </span>
            <p className="text-xs text-yellow-700 mt-1">
              Complete all training videos to access platform features.
            </p>
          </div>
        </div>
        <Link
          to="/training"
          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
        >
          <AcademicCapIcon className="h-4 w-4 mr-1" />
          Start Training
        </Link>
      </div>
    </div>
  );
};

export default TrainingStatus;

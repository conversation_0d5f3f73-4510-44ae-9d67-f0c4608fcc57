import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import { CommunicationsService } from './communications.service';
import { CreateCommunicationDto } from './dto/create-communication.dto';
import { UpdateCommunicationDto } from './dto/update-communication.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { TrainingGuard } from '../auth/guards/training.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User, Role } from '@prisma/client';

@Controller('communications')
@UseGuards(JwtAuthGuard, TrainingGuard)
export class CommunicationsController {
  constructor(private readonly communicationsService: CommunicationsService) {}

  @Post()
  create(@Body() createCommunicationDto: CreateCommunicationDto, @CurrentUser() user: User) {
    return this.communicationsService.create(createCommunicationDto, user);
  }

  @Get()
  findAll(@CurrentUser() user: User) {
    return this.communicationsService.findAll(user);
  }

  @Get('client/:clientId')
  findByClient(@Param('clientId', ParseIntPipe) clientId: number, @CurrentUser() user: User) {
    return this.communicationsService.findByClient(clientId, user);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number, @CurrentUser() user: User) {
    return this.communicationsService.findOne(id, user);
  }

  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCommunicationDto: UpdateCommunicationDto,
    @CurrentUser() user: User,
  ) {
    return this.communicationsService.update(id, updateCommunicationDto, user);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number, @CurrentUser() user: User) {
    return this.communicationsService.remove(id, user);
  }

  // Admin-only endpoint for getting all communications with filtering
  @Get('admin/all')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  findAllForAdmin(
    @Query('userId') userId?: string,
    @Query('clientId') clientId?: string,
    @Query('date') date?: string,
    @Query('type') type?: string,
  ) {
    const filters = {
      userId: userId ? parseInt(userId) : undefined,
      clientId: clientId ? parseInt(clientId) : undefined,
      date,
      type,
    };

    return this.communicationsService.findAllForAdmin(filters);
  }
}

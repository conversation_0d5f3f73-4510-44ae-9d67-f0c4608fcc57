const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function debugVideos() {
  try {
    console.log('🔍 Debugging video issues...\n');

    // Check videos in database
    const videos = await prisma.trainingVideo.findMany();
    console.log(`📹 Found ${videos.length} videos in database:`);
    
    videos.forEach((video, index) => {
      console.log(`\n${index + 1}. Video ID: ${video.id}`);
      console.log(`   Title: ${video.title}`);
      console.log(`   Filename: ${video.filename}`);
      console.log(`   File Path: ${video.filePath}`);
      console.log(`   Active: ${video.isActive}`);
      console.log(`   Required: ${video.isRequired}`);
      
      // Check if file exists
      const fileExists = fs.existsSync(video.filePath);
      console.log(`   File Exists: ${fileExists ? '✅ YES' : '❌ NO'}`);
      
      if (fileExists) {
        const stats = fs.statSync(video.filePath);
        console.log(`   File Size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
      }
    });

    // Check uploads directory
    console.log('\n📁 Checking uploads directory...');
    const uploadsDir = path.join(__dirname, 'uploads', 'videos');
    console.log(`   Directory: ${uploadsDir}`);
    
    if (fs.existsSync(uploadsDir)) {
      const files = fs.readdirSync(uploadsDir);
      console.log(`   Files found: ${files.length}`);
      files.forEach(file => {
        const filePath = path.join(uploadsDir, file);
        const stats = fs.statSync(filePath);
        console.log(`   - ${file} (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
      });
    } else {
      console.log('   ❌ Directory does not exist');
    }

    // Check user progress
    console.log('\n👥 Checking user progress...');
    const progress = await prisma.userVideoProgress.findMany({
      include: {
        user: { select: { email: true } },
        video: { select: { title: true } }
      }
    });
    
    console.log(`   Progress records: ${progress.length}`);
    progress.forEach(p => {
      console.log(`   - ${p.user.email}: ${p.video.title} (${p.isCompleted ? 'Completed' : 'Not completed'})`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugVideos();

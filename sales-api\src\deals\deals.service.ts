import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateDealDto } from './dto/create-deal.dto';
import { UpdateDealDto } from './dto/update-deal.dto';
import { User } from '@prisma/client';

@Injectable()
export class DealsService {
  constructor(private prisma: PrismaService) {}

  async create(createDealDto: CreateDealDto, user: User) {
    // Check if client exists and user has access to it
    const client = await this.prisma.client.findUnique({
      where: { id: createDealDto.clientId },
    });

    if (!client) {
      throw new NotFoundException(`Client with ID ${createDealDto.clientId} not found`);
    }

    // Check if user has access to this client (for non-admin users)
    if (user.role !== 'ADMIN') {
      const hasAccess = client.assignedUserId === user.id || client.createdById === user.id;
      if (!hasAccess) {
        throw new ForbiddenException('You can only create deals for clients assigned to you or created by you');
      }
    }

    return this.prisma.deal.create({
      data: {
        ...createDealDto,
        userId: user.id,
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  async findAll(user: User) {
    // For admin users, show all deals
    if (user.role === 'ADMIN') {
      return this.prisma.deal.findMany({
        include: {
          client: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
          user: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    }

    // For regular users, show only deals from their assigned/created clients
    return this.prisma.deal.findMany({
      where: {
        client: {
          OR: [
            { assignedUserId: user.id }, // Clients assigned to the user
            { createdById: user.id }     // Clients created by the user
          ]
        }
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: number, user: User) {
    const deal = await this.prisma.deal.findUnique({
      where: { id },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            type: true,
            email: true,
            phoneNumbers: true,
            address: true,
            assignedUserId: true,
            createdById: true,
          },
        },
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    if (!deal) {
      throw new NotFoundException(`Deal with ID ${id} not found`);
    }

    // Check access permissions for non-admin users
    if (user.role !== 'ADMIN') {
      const hasAccess = deal.client.assignedUserId === user.id || deal.client.createdById === user.id;
      if (!hasAccess) {
        throw new NotFoundException(`Deal with ID ${id} not found or access denied`);
      }
    }

    return deal;
  }

  async update(id: number, updateDealDto: UpdateDealDto, user: User) {
    const deal = await this.prisma.deal.findUnique({
      where: { id },
    });

    if (!deal) {
      throw new NotFoundException(`Deal with ID ${id} not found`);
    }

    // Only allow the deal creator or admin to update
    if (deal.userId !== user.id && user.role !== 'ADMIN') {
      throw new ForbiddenException('You can only update your own deals');
    }

    return this.prisma.deal.update({
      where: { id },
      data: updateDealDto,
      include: {
        client: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  async remove(id: number, user: User) {
    const deal = await this.prisma.deal.findUnique({
      where: { id },
    });

    if (!deal) {
      throw new NotFoundException(`Deal with ID ${id} not found`);
    }

    // Only allow the deal creator or admin to delete
    if (deal.userId !== user.id && user.role !== 'ADMIN') {
      throw new ForbiddenException('You can only delete your own deals');
    }

    return this.prisma.deal.delete({
      where: { id },
    });
  }
}

import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import AdminRoute from './components/auth/AdminRoute';
import TrainingRoute from './components/auth/TrainingRoute';
import Login from './components/auth/Login';
import DashboardLayout from './components/layout/DashboardLayout';
import AdminLayout from './components/layout/AdminLayout';
import Dashboard from './components/dashboard/Dashboard';
import AdminDashboard from './components/admin/AdminDashboard';
import ClientAssignment from './components/admin/ClientAssignment';
import AdminCommunications from './components/admin/AdminCommunications';
import UserManagement from './components/admin/UserManagement';
import ClientsList from './components/clients/ClientsList';
import ClientForm from './components/clients/ClientForm';
import ClientDetail from './components/clients/ClientDetail';
import DealsList from './components/deals/DealsList';
import DealForm from './components/deals/DealForm';
import DealDetail from './components/deals/DealDetail';
import CommunicationsList from './components/communications/CommunicationsList';
import CommunicationForm from './components/communications/CommunicationForm';
import TrainingDashboard from './components/training/TrainingDashboard';
import VideoManagement from './components/admin/VideoManagement';
import TrainingProgress from './components/admin/TrainingProgress';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<Login />} />

            {/* Protected routes for regular users */}
            <Route path="/" element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }>
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route path="dashboard" element={<TrainingRoute><Dashboard /></TrainingRoute>} />
              <Route path="clients" element={<TrainingRoute><ClientsList /></TrainingRoute>} />
              <Route path="clients/new" element={<TrainingRoute><ClientForm /></TrainingRoute>} />
              <Route path="clients/:id" element={<TrainingRoute><ClientDetail /></TrainingRoute>} />
              <Route path="clients/:id/edit" element={<TrainingRoute><ClientForm isEdit /></TrainingRoute>} />
              <Route path="deals" element={<TrainingRoute><DealsList /></TrainingRoute>} />
              <Route path="deals/new" element={<TrainingRoute><DealForm /></TrainingRoute>} />
              <Route path="deals/:id" element={<TrainingRoute><DealDetail /></TrainingRoute>} />
              <Route path="deals/:id/edit" element={<TrainingRoute><DealForm isEdit /></TrainingRoute>} />
              <Route path="communications" element={<TrainingRoute><CommunicationsList /></TrainingRoute>} />
              <Route path="communications/new" element={<TrainingRoute><CommunicationForm /></TrainingRoute>} />
              <Route path="communications/:id/edit" element={<TrainingRoute><CommunicationForm isEdit /></TrainingRoute>} />
              <Route path="training" element={<TrainingDashboard />} />
            </Route>

            {/* Admin routes */}
            <Route path="/admin" element={
              <AdminRoute>
                <AdminLayout />
              </AdminRoute>
            }>
              <Route index element={<AdminDashboard />} />
              <Route path="analytics" element={<AdminDashboard />} />
              <Route path="users" element={<UserManagement />} />
              <Route path="salesmen" element={<ClientAssignment />} />
              <Route path="clients" element={<ClientsList />} />
              <Route path="clients/new" element={<ClientForm />} />
              <Route path="clients/:id" element={<ClientDetail />} />
              <Route path="clients/:id/edit" element={<ClientForm isEdit />} />
              <Route path="deals" element={<DealsList />} />
              <Route path="deals/new" element={<DealForm />} />
              <Route path="deals/:id" element={<DealDetail />} />
              <Route path="deals/:id/edit" element={<DealForm isEdit />} />
              <Route path="communications" element={<AdminCommunications />} />
              <Route path="training/videos" element={<VideoManagement />} />
              <Route path="training/progress" element={<TrainingProgress />} />
            </Route>

            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;

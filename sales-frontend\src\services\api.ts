import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3300';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Types
export interface User {
  id: number;
  email: string;
  firstName?: string;
  lastName?: string;
  role: 'USER' | 'ADMIN';
  isActive?: boolean;
  trainingCompleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface AuthResponse {
  access_token: string;
  user: User;
}

export interface Client {
  id: number;
  name: string;
  type: 'ORGANIZATION' | 'COMPANY' | 'STORE' | 'PERSON' | 'OTHER';
  email?: string;
  phoneNumbers: string[];
  address?: string;
  importance: 'VERY_LOW' | 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH';
  notes?: string;
  assignedUserId?: number;
  assignedUser?: {
    id: number;
    email: string;
    firstName?: string;
    lastName?: string;
  };
  createdById?: number;
  createdBy?: {
    id: number;
    email: string;
    firstName?: string;
    lastName?: string;
  };
  createdAt: string;
  updatedAt: string;
  _count?: {
    deals: number;
  };
  deals?: Deal[];
}

export interface Deal {
  id: number;
  clientId: number;
  userId: number;
  amount: string;
  currency: string;
  description: string;
  details?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  client?: {
    id: number;
    name: string;
    type: string;
    email?: string;
    phoneNumbers?: string[];
    address?: string;
  };
  user?: {
    id: number;
    email: string;
    firstName?: string;
    lastName?: string;
  };
}

export interface Communication {
  id: number;
  type: 'CALL' | 'EMAIL' | 'OTHER';
  content?: string;
  followUp: boolean;
  userId: number;
  clientId: number;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: number;
    email: string;
    firstName?: string;
    lastName?: string;
  };
  client?: {
    id: number;
    name: string;
  };
}

// Auth API
export const authAPI = {
  login: async (email: string, password: string): Promise<AuthResponse> => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  register: async (userData: {
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
    role?: 'USER' | 'ADMIN';
  }): Promise<AuthResponse> => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  getProfile: async (): Promise<{ user: User }> => {
    const response = await api.get('/auth/profile');
    return response.data;
  },

  logout: async (): Promise<void> => {
    await api.post('/auth/logout');
  },
};

// Clients API
export const clientsAPI = {
  getAll: async (): Promise<Client[]> => {
    const response = await api.get('/clients');
    return response.data;
  },

  getById: async (id: number): Promise<Client> => {
    const response = await api.get(`/clients/${id}`);
    return response.data;
  },

  create: async (clientData: Omit<Client, 'id' | 'createdAt' | 'updatedAt' | 'assignedUser' | '_count' | 'deals'>): Promise<Client> => {
    const response = await api.post('/clients', clientData);
    return response.data;
  },

  update: async (id: number, clientData: Partial<Omit<Client, 'id' | 'createdAt' | 'updatedAt' | 'assignedUser' | '_count' | 'deals'>>): Promise<Client> => {
    const response = await api.patch(`/clients/${id}`, clientData);
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/clients/${id}`);
  },

  getDeals: async (id: number): Promise<Deal[]> => {
    const response = await api.get(`/clients/${id}/deals`);
    return response.data;
  },

  // Admin-only assignment functions
  assignClient: async (clientId: number, userId: number): Promise<Client> => {
    const response = await api.post(`/clients/${clientId}/assign`, { userId });
    return response.data;
  },

  unassignClient: async (clientId: number): Promise<Client> => {
    const response = await api.post(`/clients/${clientId}/unassign`);
    return response.data;
  },
};

// Deals API
export const dealsAPI = {
  getAll: async (): Promise<Deal[]> => {
    const response = await api.get('/deals');
    return response.data;
  },

  getById: async (id: number): Promise<Deal> => {
    const response = await api.get(`/deals/${id}`);
    return response.data;
  },

  create: async (dealData: {
    clientId: number;
    amount: number;
    currency?: string;
    description: string;
    details?: string;
    notes?: string;
  }): Promise<Deal> => {
    const response = await api.post('/deals', dealData);
    return response.data;
  },

  update: async (id: number, dealData: Partial<{
    clientId: number;
    amount: number;
    currency: string;
    description: string;
    details: string;
    notes: string;
  }>): Promise<Deal> => {
    const response = await api.patch(`/deals/${id}`, dealData);
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/deals/${id}`);
  },
};

// Users API
export const usersAPI = {
  getAll: async (): Promise<User[]> => {
    const response = await api.get('/users');
    return response.data;
  },

  getSalesmen: async (): Promise<User[]> => {
    const response = await api.get('/users/salesmen');
    return response.data;
  },

  getById: async (id: number): Promise<User> => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  update: async (id: number, data: Partial<User>): Promise<User> => {
    const response = await api.patch(`/users/${id}`, data);
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/users/${id}`);
  },
};

// Analytics API (Admin only)
export const analyticsAPI = {
  getOverview: async () => {
    const response = await api.get('/analytics/overview');
    return response.data;
  },

  getSalesmanPerformance: async (period?: string) => {
    const response = await api.get('/analytics/salesman-performance', {
      params: { period },
    });
    return response.data;
  },

  getDealsByPeriod: async (period?: string) => {
    const response = await api.get('/analytics/deals-by-period', {
      params: { period },
    });
    return response.data;
  },

  getClientsByPeriod: async (period?: string) => {
    const response = await api.get('/analytics/clients-by-period', {
      params: { period },
    });
    return response.data;
  },

  getRevenueTrends: async (period?: string) => {
    const response = await api.get('/analytics/revenue-trends', {
      params: { period },
    });
    return response.data;
  },

  getTopPerformers: async (period?: string) => {
    const response = await api.get('/analytics/top-performers', {
      params: { period },
    });
    return response.data;
  },

  getActivitySummary: async () => {
    const response = await api.get('/analytics/activity-summary');
    return response.data;
  },
};

// Communications API
export const communicationsAPI = {
  getAll: async (): Promise<Communication[]> => {
    const response = await api.get('/communications');
    return response.data;
  },

  getById: async (id: number): Promise<Communication> => {
    const response = await api.get(`/communications/${id}`);
    return response.data;
  },

  getByClient: async (clientId: number): Promise<Communication[]> => {
    const response = await api.get(`/communications/client/${clientId}`);
    return response.data;
  },

  create: async (communicationData: {
    type: 'CALL' | 'EMAIL' | 'OTHER';
    content?: string;
    followUp?: boolean;
    clientId: number;
  }): Promise<Communication> => {
    const response = await api.post('/communications', communicationData);
    return response.data;
  },

  update: async (id: number, communicationData: Partial<{
    type: 'CALL' | 'EMAIL' | 'OTHER';
    content: string;
    followUp: boolean;
    clientId: number;
  }>): Promise<Communication> => {
    const response = await api.patch(`/communications/${id}`, communicationData);
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/communications/${id}`);
  },

  // Admin-only endpoint
  getAllForAdmin: async (filters?: {
    userId?: number;
    clientId?: number;
    date?: string;
    type?: string;
  }): Promise<Communication[]> => {
    const response = await api.get('/communications/admin/all', {
      params: filters,
    });
    return response.data;
  },
};

// Video types
export interface TrainingVideo {
  id: number;
  title: string;
  description?: string;
  filename: string;
  filePath: string;
  duration?: number;
  order: number;
  isRequired: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface VideoProgress {
  id: number;
  userId: number;
  videoId: number;
  isCompleted: boolean;
  watchedAt?: string;
  createdAt: string;
  updatedAt: string;
  video?: TrainingVideo;
}

export interface TrainingStats {
  totalVideos: number;
  totalUsers: number;
  completedUsers: number;
  completionRate: number;
  userProgress: Array<{
    id: number;
    email: string;
    firstName?: string;
    lastName?: string;
    trainingCompleted: boolean;
    completedVideos: number;
    completedRequiredVideos: number;
  }>;
}

// Videos API
export const videosAPI = {
  // Admin endpoints
  upload: async (formData: FormData): Promise<TrainingVideo> => {
    const response = await api.post('/videos/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  getAllForAdmin: async (): Promise<TrainingVideo[]> => {
    const response = await api.get('/videos/admin/all');
    return response.data;
  },

  getStats: async (): Promise<TrainingStats> => {
    const response = await api.get('/videos/admin/stats');
    return response.data;
  },

  update: async (id: number, data: Partial<TrainingVideo>): Promise<TrainingVideo> => {
    const response = await api.patch(`/videos/admin/${id}`, data);
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/videos/admin/${id}`);
  },

  // User endpoints
  getAll: async (): Promise<TrainingVideo[]> => {
    const response = await api.get('/videos');
    return response.data;
  },

  getMyProgress: async (): Promise<VideoProgress[]> => {
    const response = await api.get('/videos/my-progress');
    return response.data;
  },

  updateProgress: async (videoId: number, isCompleted: boolean): Promise<VideoProgress> => {
    const response = await api.patch(`/videos/${videoId}/progress`, { isCompleted });
    return response.data;
  },

  getTrainingStatus: async (): Promise<{ trainingCompleted: boolean }> => {
    const response = await api.get('/videos/training-status');
    return response.data;
  },

  getStreamUrl: (videoId: number): string => {
    return `${API_BASE_URL}/videos/stream/${videoId}`;
  },
};

export default api;

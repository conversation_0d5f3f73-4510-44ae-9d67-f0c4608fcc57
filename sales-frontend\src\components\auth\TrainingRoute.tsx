import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { videosAPI } from '../../services/api';

interface TrainingRouteProps {
  children: React.ReactNode;
}

const TrainingRoute: React.FC<TrainingRouteProps> = ({ children }) => {
  const { user } = useAuth();
  const location = useLocation();
  const [trainingCompleted, setTrainingCompleted] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkTrainingStatus();
  }, []);

  const checkTrainingStatus = async () => {
    try {
      // Skip training check for admins
      if (user?.role === 'ADMIN') {
        setTrainingCompleted(true);
        setLoading(false);
        return;
      }

      // Skip training check for training-related routes
      if (location.pathname.startsWith('/training')) {
        setTrainingCompleted(true);
        setLoading(false);
        return;
      }

      const status = await videosAPI.getTrainingStatus();
      setTrainingCompleted(status.trainingCompleted);
    } catch (error) {
      console.error('Failed to check training status:', error);
      // If we can't check training status, assume incomplete for safety
      setTrainingCompleted(false);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Redirect to training if not completed
  if (!trainingCompleted) {
    return <Navigate to="/training" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

export default TrainingRoute;

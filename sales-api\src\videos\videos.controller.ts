import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Req,
  Res,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Request, Response } from 'express';
import { VideosService } from './videos.service';
import { CreateVideoDto } from './dto/create-video.dto';
import { UpdateVideoDto } from './dto/update-video.dto';
import { UpdateVideoProgressDto } from './dto/video-progress.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { SkipTraining } from '../auth/decorators/skip-training.decorator';
import { Public } from '../auth/decorators/public.decorator';
import { User, Role } from '@prisma/client';
import { diskStorage } from 'multer';
import { extname } from 'path';
import * as path from 'path';
import * as fs from 'fs';

// Multer configuration for video uploads
const videoStorage = diskStorage({
  destination: './uploads/videos',
  filename: (req, file, callback) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    const ext = extname(file.originalname);
    callback(null, `video-${uniqueSuffix}${ext}`);
  },
});

const videoFileFilter = (req: any, file: any, callback: any) => {
  const allowedMimeTypes = [
    'video/mp4',
    'video/mpeg',
    'video/quicktime',
    'video/x-msvideo',
    'video/webm',
  ];

  if (allowedMimeTypes.includes(file.mimetype)) {
    callback(null, true);
  } else {
    callback(new BadRequestException('Only video files are allowed'), false);
  }
};

@Controller('videos')
@UseGuards(JwtAuthGuard)
@SkipTraining()
export class VideosController {
  constructor(private readonly videosService: VideosService) {}

  // Admin endpoints
  @Post('upload')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  @UseInterceptors(
    FileInterceptor('video', {
      storage: videoStorage,
      fileFilter: videoFileFilter,
      limits: {
        fileSize: 500 * 1024 * 1024, // 500MB limit
      },
    }),
  )
  async uploadVideo(
    @UploadedFile() file: Express.Multer.File,
    @Body() createVideoDto: any, // Use any to handle FormData string values
  ) {
    if (!file) {
      throw new BadRequestException('Video file is required');
    }

    // Transform FormData string values to proper types
    const transformedDto: CreateVideoDto = {
      title: createVideoDto.title,
      description: createVideoDto.description || undefined,
      duration: createVideoDto.duration ? parseInt(createVideoDto.duration) : undefined,
      order: createVideoDto.order ? parseInt(createVideoDto.order) : undefined,
      isRequired: createVideoDto.isRequired === 'true' || createVideoDto.isRequired === true,
      isActive: createVideoDto.isActive === 'true' || createVideoDto.isActive === true,
    };

    // Ensure the file path is absolute and works in container
    const absolutePath = path.resolve(file.path);
    console.log(`📁 File uploaded:`, {
      originalPath: file.path,
      absolutePath: absolutePath,
      filename: file.filename,
      destination: file.destination
    });

    return this.videosService.create(
      transformedDto,
      file.filename,
      absolutePath,
    );
  }

  @Get('admin/all')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  findAllForAdmin() {
    return this.videosService.findAllForAdmin();
  }

  @Get('admin/stats')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  getTrainingStats() {
    return this.videosService.getTrainingStats();
  }

  @Patch('admin/:id')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  update(@Param('id', ParseIntPipe) id: number, @Body() updateVideoDto: UpdateVideoDto) {
    return this.videosService.update(id, updateVideoDto);
  }

  @Delete('admin/:id')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.videosService.remove(id);
  }

  // Test endpoint
  @Get('test')
  @SkipTraining()
  testEndpoint() {
    console.log('🧪 Videos test endpoint called');
    return { message: 'Videos controller is working!', timestamp: new Date().toISOString() };
  }

  // Debug endpoint for file system status
  @Get('debug/files')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async debugFiles() {
    try {
      const videos = await this.videosService.findAllForAdmin();
      const uploadsDir = './uploads/videos';

      const result = {
        uploadsDirectory: {
          path: uploadsDir,
          exists: fs.existsSync(uploadsDir),
          files: fs.existsSync(uploadsDir) ? fs.readdirSync(uploadsDir) : [],
        },
        workingDirectory: process.cwd(),
        videos: videos.map((video) => ({
          id: video.id,
          title: video.title,
          filename: video.filename,
          storedPath: video.filePath,
          fileExists: fs.existsSync(video.filePath),
          alternativePaths: [
            { path: path.resolve(video.filePath), exists: fs.existsSync(path.resolve(video.filePath)) },
            { path: path.join(process.cwd(), video.filePath), exists: fs.existsSync(path.join(process.cwd(), video.filePath)) },
            { path: path.join('./uploads/videos', video.filename), exists: fs.existsSync(path.join('./uploads/videos', video.filename)) },
          ]
        }))
      };

      return result;
    } catch (error) {
      return { error: error.message };
    }
  }

  // User endpoints
  @Get()
  findAll() {
    console.log('📋 Getting all videos');
    return this.videosService.findAll();
  }

  @Get('my-progress')
  getUserProgress(@CurrentUser() user: User) {
    console.log(`👤 Getting progress for user: ${user.email} (ID: ${user.id})`);
    return this.videosService.getUserProgress(user.id);
  }

  @Patch(':videoId/progress')
  updateProgress(
    @Param('videoId', ParseIntPipe) videoId: number,
    @Body() updateProgressDto: UpdateVideoProgressDto,
    @CurrentUser() user: User,
  ) {
    return this.videosService.updateProgress(user.id, videoId, updateProgressDto);
  }

  @Get('training-status')
  async getTrainingStatus(@CurrentUser() user: User) {
    const isCompleted = await this.videosService.checkUserTrainingCompletion(user.id);
    return { trainingCompleted: isCompleted };
  }

  // Health check endpoint
  @Get('health')
  @Public()
  async healthCheck() {
    try {
      const videoCount = await this.videosService.findAll();
      const uploadsDir = './uploads/videos';

      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        videoCount: videoCount.length,
        uploadsDirectory: {
          exists: fs.existsSync(uploadsDir),
          path: uploadsDir,
          absolutePath: path.resolve(uploadsDir)
        },
        workingDirectory: process.cwd()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Video streaming endpoint - Public access for video streaming
  @Get('stream/:id')
  @Public()
  async streamVideo(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const videoWithPath = await this.videosService.getVideoWithResolvedPath(id);

      const actualFilePath = videoWithPath.resolvedFilePath;
      const stat = fs.statSync(actualFilePath);
      const fileSize = stat.size;

      // Determine content type based on file extension
      const ext = path.extname(videoWithPath.filename).toLowerCase();
      let contentType = 'video/mp4'; // default

      switch (ext) {
        case '.mp4':
          contentType = 'video/mp4';
          break;
        case '.webm':
          contentType = 'video/webm';
          break;
        case '.mov':
          contentType = 'video/quicktime';
          break;
        case '.avi':
          contentType = 'video/x-msvideo';
          break;
        case '.mpeg':
        case '.mpg':
          contentType = 'video/mpeg';
          break;
        default:
          console.warn(`⚠️ Unknown video extension: ${ext}, using video/mp4`);
      }

      // Handle HTTP Range requests for video streaming
      const range = req.headers.range;

      if (range) {
        // Parse range header
        const parts = range.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        let end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;

        // Ensure end doesn't exceed file size
        end = Math.min(end, fileSize - 1);

        const chunksize = (end - start) + 1;

        console.log(`🎯 Range request:`, { start, end, chunksize, fileSize });

        // Validate range
        if (start >= fileSize || end >= fileSize || start > end) {
          console.error(`❌ Invalid range: start=${start}, end=${end}, fileSize=${fileSize}`);
          res.status(416).set({
            'Content-Range': `bytes */${fileSize}`,
          });
          res.end();
          return;
        }

        // Set partial content headers optimized for video streaming
        res.status(206);
        res.set({
          'Content-Range': `bytes ${start}-${end}/${fileSize}`,
          'Accept-Ranges': 'bytes',
          'Content-Length': chunksize.toString(),
          'Content-Type': contentType,
          'Cache-Control': 'public, max-age=86400', // 24 hours cache
          'Connection': 'keep-alive',
          'Transfer-Encoding': 'chunked',
        });

        console.log(`📤 Streaming range: ${start}-${end}/${fileSize}`);

        // Create and pipe the stream directly
        const stream = fs.createReadStream(actualFilePath, { start, end });

        stream.on('error', (error) => {
          console.error(`❌ Stream error:`, error);
          if (!res.headersSent) {
            res.status(500).end();
          }
        });

        stream.on('open', () => {
          console.log(`✅ Streaming range: ${start}-${end}`);
        });

        stream.pipe(res);

      } else {
        // No range request - send entire file
        console.log(`📁 Sending entire file`);

        res.set({
          'Content-Type': contentType,
          'Content-Length': fileSize.toString(),
          'Accept-Ranges': 'bytes',
          'Cache-Control': 'public, max-age=86400', // 24 hours cache
          'Connection': 'keep-alive',
        });

        console.log(`📤 Streaming entire file`);

        const stream = fs.createReadStream(actualFilePath);

        stream.on('error', (error) => {
          console.error(`❌ Stream error:`, error);
          if (!res.headersSent) {
            res.status(500).end();
          }
        });

        stream.on('open', () => {
          console.log(`✅ Streaming entire file`);
        });

        stream.pipe(res);
      }

    } catch (error) {
      console.error(`❌ Error in streamVideo:`, error);
      throw error;
    }
  }
}

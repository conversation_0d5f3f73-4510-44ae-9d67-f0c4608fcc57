import { Injectable, NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from '@prisma/client';

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async findAll() {
    return this.prisma.user.findMany({
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findSalesmen() {
    return this.prisma.user.findMany({
      where: {
        role: 'USER',
        isActive: true,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: number) {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        trainingCompleted: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async update(id: number, updateUserDto: UpdateUserDto, currentUser: User) {
    // Check if user exists
    const existingUser = await this.findOne(id);

    // Prevent users from editing themselves (to avoid role escalation)
    if (currentUser.id === id) {
      throw new ForbiddenException('You cannot edit your own account');
    }

    // Check if email is being changed and if it's already taken
    if (updateUserDto.email && updateUserDto.email !== existingUser.email) {
      const emailExists = await this.prisma.user.findUnique({
        where: { email: updateUserDto.email },
      });

      if (emailExists) {
        throw new ConflictException('Email is already taken');
      }
    }

    return this.prisma.user.update({
      where: { id },
      data: updateUserDto,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        trainingCompleted: true,
        createdAt: true,
        updatedAt: true,
      },
    });
  }

  async delete(id: number, currentUser: User) {
    // Check if user exists (will throw NotFoundException if not found)
    await this.findOne(id);

    // Prevent users from deleting themselves
    if (currentUser.id === id) {
      throw new ForbiddenException('You cannot delete your own account');
    }

    // Use a transaction to safely remove relationships and delete the user
    await this.prisma.$transaction(async (prisma) => {
      // Remove user from assigned clients (set assignedUserId to null)
      await prisma.client.updateMany({
        where: { assignedUserId: id },
        data: { assignedUserId: null },
      });

      // For clients created by this user, we'll keep the createdById reference
      // as it's historical data, but you could also set it to null if preferred
      // await prisma.client.updateMany({
      //   where: { createdById: id },
      //   data: { createdById: null },
      // });

      // Remove user from deals (set userId to null)
      await prisma.deal.updateMany({
        where: { userId: id },
        data: { userId: null },
      });

      // Remove user from communications (set userId to null)
      await prisma.communication.updateMany({
        where: { userId: id },
        data: { userId: null },
      });

      // Delete user video progress records
      await prisma.userVideoProgress.deleteMany({
        where: { userId: id },
      });

      // Finally, delete the user
      await prisma.user.delete({
        where: { id },
      });
    });

    return { message: 'User deleted successfully' };
  }
}

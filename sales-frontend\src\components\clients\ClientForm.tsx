import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { Client, clientsAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import {
  PlusIcon,
  MinusIcon,
  ArrowLeftIcon,
  BuildingOfficeIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  BuildingStorefrontIcon,
  GlobeAltIcon,
} from '@heroicons/react/24/outline';

interface ClientFormProps {
  isEdit?: boolean;
}

const ClientForm: React.FC<ClientFormProps> = ({ isEdit = false }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const location = useLocation();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  // Check if we're in admin context
  const isAdminContext = location.pathname.startsWith('/admin');

  // Helper function to generate correct paths
  const getClientPath = (path: string) => {
    return isAdminContext ? `/admin${path}` : path;
  };
  const [formData, setFormData] = useState({
    name: '',
    type: 'COMPANY' as Client['type'],
    email: '',
    phoneNumbers: [''],
    address: '',
    importance: 'MEDIUM' as Client['importance'],
    notes: '',
    assignedUserId: undefined as number | undefined,
  });

  useEffect(() => {
    if (isEdit && id) {
      fetchClient();
    }
  }, [isEdit, id]);

  const fetchClient = async () => {
    try {
      setLoading(true);
      const client = await clientsAPI.getById(Number(id));
      setFormData({
        name: client.name,
        type: client.type,
        email: client.email || '',
        phoneNumbers: client.phoneNumbers.length > 0 ? client.phoneNumbers : [''],
        address: client.address || '',
        importance: client.importance,
        notes: client.notes || '',
        assignedUserId: client.assignedUserId || undefined,
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch client');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'assignedUserId' ? (value ? Number(value) : undefined) : value,
    }));
    // Clear field error when user starts typing
    if (fieldErrors[name]) {
      setFieldErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handlePhoneChange = (index: number, value: string) => {
    const newPhones = [...formData.phoneNumbers];
    newPhones[index] = value;
    setFormData(prev => ({ ...prev, phoneNumbers: newPhones }));
    // Clear phone errors when user starts typing
    if (fieldErrors.phoneNumbers) {
      setFieldErrors(prev => ({ ...prev, phoneNumbers: '' }));
    }
  };

  const addPhoneNumber = () => {
    setFormData(prev => ({
      ...prev,
      phoneNumbers: [...prev.phoneNumbers, ''],
    }));
  };

  const removePhoneNumber = (index: number) => {
    if (formData.phoneNumbers.length > 1) {
      const newPhones = formData.phoneNumbers.filter((_, i) => i !== index);
      setFormData(prev => ({ ...prev, phoneNumbers: newPhones }));
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      errors.name = 'Client name is required';
    }
    
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }
    
    const validPhones = formData.phoneNumbers.filter(phone => phone.trim() !== '');
    if (validPhones.length === 0) {
      errors.phoneNumbers = 'At least one phone number is required';
    }
    
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    
    if (!validateForm()) {
      return;
    }
    
    setSaving(true);

    try {
      const submitData = {
        ...formData,
        phoneNumbers: formData.phoneNumbers.filter(phone => phone.trim() !== ''),
      };

      if (isEdit && id) {
        await clientsAPI.update(Number(id), submitData);
        setSuccess('Client updated successfully!');
      } else {
        await clientsAPI.create(submitData);
        setSuccess('Client created successfully!');
      }
      
      setTimeout(() => navigate(getClientPath('/clients')), 1500);
    } catch (err: any) {
      setError(err.response?.data?.message || `Failed to ${isEdit ? 'update' : 'create'} client`);
    } finally {
      setSaving(false);
    }
  };

  const getClientTypeIcon = (type: string) => {
    switch (type) {
      case 'COMPANY':
        return <BuildingOfficeIcon className="h-5 w-5 text-blue-500" />;
      case 'STORE':
        return <BuildingStorefrontIcon className="h-5 w-5 text-green-500" />;
      case 'PERSON':
        return <UserIcon className="h-5 w-5 text-purple-500" />;
      case 'ORGANIZATION':
        return <GlobeAltIcon className="h-5 w-5 text-orange-500" />;
      default:
        return <BuildingOfficeIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'VERY_LOW':
        return 'text-gray-600 bg-gray-100 border-gray-300';
      case 'LOW':
        return 'text-blue-600 bg-blue-100 border-blue-300';
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-100 border-yellow-300';
      case 'HIGH':
        return 'text-orange-600 bg-orange-100 border-orange-300';
      case 'VERY_HIGH':
        return 'text-red-600 bg-red-100 border-red-300';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-300';
    }
  };

  if (loading && isEdit) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-sm text-gray-600">Loading client information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate(getClientPath('/clients'))}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4 transition-colors"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Clients
          </button>
          
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <div className="h-16 w-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl flex items-center justify-center shadow-sm">
                {getClientTypeIcon(formData.type)}
              </div>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {isEdit ? 'Edit Client' : 'Create New Client'}
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                {isEdit ? 'Update client information and settings' : 'Add a new client to your portfolio'}
              </p>
            </div>
          </div>
        </div>

        {/* Success Message */}
        {success && (
          <div className="mb-6 rounded-lg bg-green-50 p-4 border border-green-200 shadow-sm">
            <div className="flex">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">{success}</p>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="mb-6 rounded-lg bg-red-50 p-4 border border-red-200 shadow-sm">
            <div className="flex">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information Card */}
          <div className="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl">
            <div className="px-4 py-6 sm:p-8">
              <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                <div className="sm:col-span-6">
                  <h3 className="text-lg font-semibold leading-7 text-gray-900 flex items-center">
                    <BuildingOfficeIcon className="h-5 w-5 mr-2 text-gray-400" />
                    Basic Information
                  </h3>
                  <p className="mt-1 text-sm leading-6 text-gray-600">
                    Essential details about the client
                  </p>
                </div>

                {/* Client Name */}
                <div className="sm:col-span-4">
                  <label htmlFor="name" className="block text-sm font-medium leading-6 text-gray-900">
                    Client Name *
                  </label>
                  <div className="mt-2">
                    <input
                      type="text"
                      name="name"
                      id="name"
                      required
                      className={`block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 transition-colors ${
                        fieldErrors.name 
                          ? 'ring-red-300 focus:ring-red-500' 
                          : 'ring-gray-300 focus:ring-primary-600'
                      }`}
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="Enter client name"
                    />
                    {fieldErrors.name && (
                      <p className="mt-2 text-sm text-red-600 flex items-center">
                        <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                        {fieldErrors.name}
                      </p>
                    )}
                  </div>
                </div>

                {/* Client Type */}
                <div className="sm:col-span-2">
                  <label htmlFor="type" className="block text-sm font-medium leading-6 text-gray-900">
                    Type *
                  </label>
                  <div className="mt-2">
                    <select
                      name="type"
                      id="type"
                      required
                      className="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6"
                      value={formData.type}
                      onChange={handleChange}
                    >
                      <option value="COMPANY">Company</option>
                      <option value="ORGANIZATION">Organization</option>
                      <option value="STORE">Store</option>
                      <option value="PERSON">Person</option>
                      <option value="OTHER">Other</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information Card */}
          <div className="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl">
            <div className="px-4 py-6 sm:p-8">
              <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                <div className="sm:col-span-6">
                  <h3 className="text-lg font-semibold leading-7 text-gray-900 flex items-center">
                    <EnvelopeIcon className="h-5 w-5 mr-2 text-gray-400" />
                    Contact Information
                  </h3>
                  <p className="mt-1 text-sm leading-6 text-gray-600">
                    How to reach this client
                  </p>
                </div>

                {/* Email */}
                <div className="sm:col-span-3">
                  <label htmlFor="email" className="block text-sm font-medium leading-6 text-gray-900">
                    Email Address
                  </label>
                  <div className="mt-2">
                    <div className="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-600">
                      <span className="flex select-none items-center pl-3 text-gray-500 sm:text-sm">
                        <EnvelopeIcon className="h-4 w-4" />
                      </span>
                      <input
                        type="email"
                        name="email"
                        id="email"
                        className={`block flex-1 border-0 bg-transparent py-2.5 pl-2 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6 ${
                          fieldErrors.email ? 'text-red-900' : ''
                        }`}
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    {fieldErrors.email && (
                      <p className="mt-2 text-sm text-red-600 flex items-center">
                        <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                        {fieldErrors.email}
                      </p>
                    )}
                  </div>
                </div>

                {/* Phone Numbers */}
                <div className="sm:col-span-3">
                  <label className="block text-sm font-medium leading-6 text-gray-900">
                    Phone Numbers *
                  </label>
                  <div className="mt-2 space-y-3">
                    {formData.phoneNumbers.map((phone, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <div className="flex-1 flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-600">
                          <span className="flex select-none items-center pl-3 text-gray-500 sm:text-sm">
                            <PhoneIcon className="h-4 w-4" />
                          </span>
                          <input
                            type="tel"
                            value={phone}
                            onChange={(e) => handlePhoneChange(index, e.target.value)}
                            className="block flex-1 border-0 bg-transparent py-2.5 pl-2 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                            placeholder="+****************"
                          />
                        </div>
                        {formData.phoneNumbers.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removePhoneNumber(index)}
                            className="inline-flex items-center p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors"
                          >
                            <MinusIcon className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    ))}
                    {fieldErrors.phoneNumbers && (
                      <p className="text-sm text-red-600 flex items-center">
                        <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                        {fieldErrors.phoneNumbers}
                      </p>
                    )}
                    <button
                      type="button"
                      onClick={addPhoneNumber}
                      className="inline-flex items-center px-3 py-2 text-sm font-medium text-primary-700 bg-primary-50 border border-primary-200 rounded-md hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                    >
                      <PlusIcon className="h-4 w-4 mr-1" />
                      Add Phone Number
                    </button>
                  </div>
                </div>

                {/* Address */}
                <div className="sm:col-span-6">
                  <label htmlFor="address" className="block text-sm font-medium leading-6 text-gray-900">
                    Address
                  </label>
                  <div className="mt-2">
                    <div className="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-600">
                      <span className="flex select-none items-start pt-3 pl-3 text-gray-500 sm:text-sm">
                        <MapPinIcon className="h-4 w-4" />
                      </span>
                      <textarea
                        name="address"
                        id="address"
                        rows={3}
                        className="block flex-1 border-0 bg-transparent py-2.5 pl-2 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6 resize-none"
                        value={formData.address}
                        onChange={handleChange}
                        placeholder="123 Main St, City, State 12345"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information Card */}
          <div className="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl">
            <div className="px-4 py-6 sm:p-8">
              <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                <div className="sm:col-span-6">
                  <h3 className="text-lg font-semibold leading-7 text-gray-900 flex items-center">
                    <DocumentTextIcon className="h-5 w-5 mr-2 text-gray-400" />
                    Additional Information
                  </h3>
                  <p className="mt-1 text-sm leading-6 text-gray-600">
                    Priority level and notes about this client
                  </p>
                </div>

                {/* Importance Level */}
                <div className="sm:col-span-3">
                  <label htmlFor="importance" className="block text-sm font-medium leading-6 text-gray-900">
                    Priority Level
                  </label>
                  <div className="mt-2">
                    <select
                      name="importance"
                      id="importance"
                      className="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6"
                      value={formData.importance}
                      onChange={handleChange}
                    >
                      <option value="VERY_LOW">Very Low</option>
                      <option value="LOW">Low</option>
                      <option value="MEDIUM">Medium</option>
                      <option value="HIGH">High</option>
                      <option value="VERY_HIGH">Very High</option>
                    </select>
                  </div>
                  <div className="mt-3">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getImportanceColor(formData.importance)}`}>
                      {formData.importance.replace('_', ' ')}
                    </span>
                  </div>
                </div>

                {/* Notes */}
                <div className="sm:col-span-6">
                  <label htmlFor="notes" className="block text-sm font-medium leading-6 text-gray-900">
                    Notes
                  </label>
                  <div className="mt-2">
                    <textarea
                      name="notes"
                      id="notes"
                      rows={4}
                      className="block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6"
                      value={formData.notes}
                      onChange={handleChange}
                      placeholder="Additional notes about the client, preferences, or important details..."
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end gap-x-6 px-4 py-4 sm:px-8">
            <button
              type="button"
              onClick={() => navigate(getClientPath('/clients'))}
              className="text-sm font-semibold leading-6 text-gray-900 hover:text-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving}
              className="rounded-md bg-primary-600 px-6 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {saving ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEdit ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                isEdit ? 'Update Client' : 'Create Client'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ClientForm;

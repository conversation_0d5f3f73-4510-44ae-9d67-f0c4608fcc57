// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("External_URL")
}

enum Role {
  ADMIN
  USER
}

enum ClientType {
  ORGANIZATION
  COMPANY
  STORE
  PERSON
  OTHER
}

enum ClientImportance {
  VERY_LOW
  LOW
  MEDIUM
  HIGH
  VERY_HIGH
}

enum CommunicationType {
  CALL
  EMAIL
  OTHER
}

model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  password  String
  firstName String?
  lastName  String?
  role      Role     @default(USER)
  isActive  Boolean  @default(true)
  trainingCompleted Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  assignedClients Client[]        @relation("AssignedClients")
  createdClients  Client[]        @relation("CreatedClients")
  deals           Deal[]
  communications  Communication[]
  videoProgress   UserVideoProgress[]

  @@map("users")
}

model Client {
  id           Int              @id @default(autoincrement())
  name         String
  type         ClientType
  email        String?
  phoneNumbers String[]
  address      String?
  importance   ClientImportance @default(MEDIUM)
  notes        String?
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  // Relations
  assignedUserId Int?
  assignedUser   User? @relation("AssignedClients", fields: [assignedUserId], references: [id], onDelete: SetNull)
  createdById    Int?
  createdBy      User? @relation("CreatedClients", fields: [createdById], references: [id], onDelete: SetNull)
  deals          Deal[]
  communications Communication[]

  @@map("clients")
}

model Deal {
  id          Int      @id @default(autoincrement())
  clientId    Int
  userId      Int?
  amount      Decimal  @db.Decimal(10, 2)
  currency    String   @default("USD")
  description String
  details     String?
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  client Client @relation(fields: [clientId], references: [id], onDelete: Cascade)
  user   User?  @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("deals")
}

model Communication {
  id        Int               @id @default(autoincrement())
  type      CommunicationType
  content   String?
  followUp  Boolean           @default(false)
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt

  // Relations
  userId   Int
  user     User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  clientId Int
  client   Client @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@map("communications")
}

model TrainingVideo {
  id          Int      @id @default(autoincrement())
  title       String
  description String?
  filename    String
  filePath    String
  duration    Int?     // Duration in seconds
  order       Int      @default(0) // Order for displaying videos
  isRequired  Boolean  @default(true)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userProgress UserVideoProgress[]

  @@map("training_videos")
}

model UserVideoProgress {
  id          Int      @id @default(autoincrement())
  userId      Int
  videoId     Int
  isCompleted Boolean  @default(false)
  watchedAt   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user  User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  video TrainingVideo @relation(fields: [videoId], references: [id], onDelete: Cascade)

  // Ensure one progress record per user per video
  @@unique([userId, videoId])
  @@map("user_video_progress")
}

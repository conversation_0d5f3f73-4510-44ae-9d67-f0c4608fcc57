import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { ClientsService } from './clients.service';
import { CreateClientDto } from './dto/create-client.dto';
import { UpdateClientDto } from './dto/update-client.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { TrainingGuard } from '../auth/guards/training.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User, Role } from '@prisma/client';

@Controller('clients')
@UseGuards(JwtAuthGuard, TrainingGuard)
export class ClientsController {
  constructor(private readonly clientsService: ClientsService) {}

  @Post()
  create(@Body() createClientDto: CreateClientDto, @CurrentUser() user: User) {
    return this.clientsService.create(createClientDto, user);
  }

  @Get()
  findAll(@CurrentUser() user: User) {
    return this.clientsService.findAll(user);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number, @CurrentUser() user: User) {
    return this.clientsService.findOne(id, user);
  }

  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateClientDto: UpdateClientDto,
    @CurrentUser() user: User,
  ) {
    return this.clientsService.update(id, updateClientDto, user);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number, @CurrentUser() user: User) {
    return this.clientsService.remove(id, user);
  }

  @Get(':id/deals')
  getClientDeals(@Param('id', ParseIntPipe) id: number) {
    return this.clientsService.getClientDeals(id);
  }

  // Admin-only endpoints for client assignment
  @Post(':id/assign')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  assignClient(
    @Param('id', ParseIntPipe) clientId: number,
    @Body('userId', ParseIntPipe) userId: number,
    @CurrentUser() user: User,
  ) {
    return this.clientsService.assignClient(clientId, userId, user);
  }

  @Post(':id/unassign')
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  unassignClient(
    @Param('id', ParseIntPipe) clientId: number,
    @CurrentUser() user: User,
  ) {
    return this.clientsService.unassignClient(clientId, user);
  }
}

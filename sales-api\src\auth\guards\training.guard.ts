import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PrismaService } from '../../prisma/prisma.service';
import { Role } from '@prisma/client';

@Injectable()
export class TrainingGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private prisma: PrismaService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if training is required for this route
    const skipTraining = this.reflector.get<boolean>('skipTraining', context.getHandler()) ||
                        this.reflector.get<boolean>('skipTraining', context.getClass());

    if (skipTraining) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // Skip training check for admins
    if (user.role === Role.ADMIN) {
      return true;
    }

    // Check if user has completed training
    if (!user.trainingCompleted) {
      // Double-check by querying the database for the most up-to-date status
      const requiredVideos = await this.prisma.trainingVideo.findMany({
        where: { isRequired: true, isActive: true },
        select: { id: true },
      });

      const completedVideos = await this.prisma.userVideoProgress.findMany({
        where: {
          userId: user.id,
          isCompleted: true,
          videoId: { in: requiredVideos.map(v => v.id) },
        },
        select: { videoId: true },
      });

      const isCompleted = completedVideos.length === requiredVideos.length;

      if (!isCompleted) {
        throw new ForbiddenException(
          'You must complete all required training videos before accessing this feature. Please visit the training section to complete your training.',
        );
      }

      // Update user's training status if they've completed it
      await this.prisma.user.update({
        where: { id: user.id },
        data: { trainingCompleted: true },
      });
    }

    return true;
  }
}

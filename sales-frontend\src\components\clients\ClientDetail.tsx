import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate, useLocation } from 'react-router-dom';
import { Client, Deal, Communication, clientsAPI, dealsAPI, communicationsAPI } from '../../services/api';
import {
  PencilIcon,
  TrashIcon,
  PlusIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  UserIcon,
  CurrencyDollarIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';

const ClientDetail: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [client, setClient] = useState<Client | null>(null);
  const [communications, setCommunications] = useState<Communication[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Check if we're in admin context
  const isAdminContext = location.pathname.startsWith('/admin');

  // Helper function to generate correct paths
  const getClientPath = (path: string) => {
    return isAdminContext ? `/admin${path}` : path;
  };
  const [showAddDeal, setShowAddDeal] = useState(false);
  const [dealForm, setDealForm] = useState({
    amount: '',
    currency: 'USD',
    description: '',
    details: '',
    notes: '',
  });

  const [showAddCommunication, setShowAddCommunication] = useState(false);
  const [communicationForm, setCommunicationForm] = useState({
    type: 'CALL' as 'CALL' | 'EMAIL' | 'OTHER',
    content: '',
    followUp: false,
  });

  useEffect(() => {
    if (id) {
      fetchClient();
    }
  }, [id]);

  const fetchClient = async () => {
    try {
      setLoading(true);
      const [clientData, communicationsData] = await Promise.all([
        clientsAPI.getById(Number(id)),
        communicationsAPI.getByClient(Number(id))
      ]);
      setClient(clientData);
      setCommunications(communicationsData);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch client');
    } finally {
      setLoading(false);
    }
  };

  const handleAddDeal = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await dealsAPI.create({
        clientId: Number(id),
        amount: parseFloat(dealForm.amount),
        currency: dealForm.currency,
        description: dealForm.description,
        details: dealForm.details || undefined,
        notes: dealForm.notes || undefined,
      });

      setShowAddDeal(false);
      setDealForm({ amount: '', currency: 'USD', description: '', details: '', notes: '' });
      fetchClient(); // Refresh to show new deal
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create deal');
    }
  };

  const handleAddCommunication = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await communicationsAPI.create({
        clientId: Number(id),
        type: communicationForm.type,
        content: communicationForm.content || undefined,
        followUp: communicationForm.followUp,
      });

      setShowAddCommunication(false);
      setCommunicationForm({ type: 'CALL', content: '', followUp: false });
      fetchClient(); // Refresh to show new communication
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create communication');
    }
  };

  const handleDeleteCommunication = async (commId: number) => {
    if (window.confirm('Are you sure you want to delete this communication?')) {
      try {
        await communicationsAPI.delete(commId);
        setCommunications(communications.filter(comm => comm.id !== commId));
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to delete communication');
      }
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'CALL':
        return <PhoneIcon className="h-5 w-5" />;
      case 'EMAIL':
        return <EnvelopeIcon className="h-5 w-5" />;
      default:
        return <ChatBubbleLeftRightIcon className="h-5 w-5" />;
    }
  };

  const getTypeBadge = (type: string) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    switch (type) {
      case 'CALL':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'EMAIL':
        return `${baseClasses} bg-green-100 text-green-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getImportanceBadge = (importance: string) => {
    const colors = {
      VERY_LOW: 'bg-gray-100 text-gray-800',
      LOW: 'bg-blue-100 text-blue-800',
      MEDIUM: 'bg-yellow-100 text-yellow-800',
      HIGH: 'bg-orange-100 text-orange-800',
      VERY_HIGH: 'bg-red-100 text-red-800',
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[importance as keyof typeof colors]}`}>
        {importance.replace('_', ' ')}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">Client not found</h3>
        <Link to={getClientPath("/clients")} className="mt-4 text-primary-600 hover:text-primary-500">
          Back to clients
        </Link>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8">
      {error && (
        <div className="mb-4 rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}

      {/* Header */}
      <div className="lg:flex lg:items-center lg:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            {client.name}
          </h2>
          <div className="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
            <div className="mt-2 flex items-center text-sm text-gray-500">
              <span className="capitalize">{client.type.toLowerCase()}</span>
            </div>
            <div className="mt-2 flex items-center text-sm text-gray-500">
              {getImportanceBadge(client.importance)}
            </div>
          </div>
        </div>
        <div className="mt-5 flex lg:mt-0 lg:ml-4">
          <span className="sm:ml-3">
            <Link
              to={getClientPath(`/clients/${client.id}/edit`)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PencilIcon className="-ml-1 mr-2 h-5 w-5 text-gray-500" />
              Edit
            </Link>
          </span>
        </div>
      </div>

      <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Client Information */}
        <div className="lg:col-span-2">
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Client Information</h3>
            </div>
            <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
              <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Email</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {client.email ? (
                      <div className="flex items-center">
                        <EnvelopeIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <a href={`mailto:${client.email}`} className="text-primary-600 hover:text-primary-500">
                          {client.email}
                        </a>
                      </div>
                    ) : (
                      'No email provided'
                    )}
                  </dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">Phone Numbers</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {client.phoneNumbers.length > 0 ? (
                      <div className="space-y-1">
                        {client.phoneNumbers.map((phone, index) => (
                          <div key={index} className="flex items-center">
                            <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                            <a href={`tel:${phone}`} className="text-primary-600 hover:text-primary-500">
                              {phone}
                            </a>
                          </div>
                        ))}
                      </div>
                    ) : (
                      'No phone numbers provided'
                    )}
                  </dd>
                </div>

                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">Address</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {client.address ? (
                      <div className="flex items-start">
                        <MapPinIcon className="h-4 w-4 text-gray-400 mr-2 mt-0.5" />
                        <span>{client.address}</span>
                      </div>
                    ) : (
                      'No address provided'
                    )}
                  </dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">Assigned Salesperson</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {client.assignedUser ? (
                      <div className="flex items-center">
                        <UserIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <span>{client.assignedUser.firstName} {client.assignedUser.lastName}</span>
                      </div>
                    ) : (
                      'Unassigned'
                    )}
                  </dd>
                </div>

                <div>
                  <dt className="text-sm font-medium text-gray-500">Created</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {new Date(client.createdAt).toLocaleDateString()}
                  </dd>
                </div>

                {client.notes && (
                  <div className="sm:col-span-2">
                    <dt className="text-sm font-medium text-gray-500">Notes</dt>
                    <dd className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{client.notes}</dd>
                  </div>
                )}
              </dl>
            </div>
          </div>
        </div>

        {/* Deals */}
        <div>
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Deals ({client.deals?.length || 0})
              </h3>
              <button
                onClick={() => setShowAddDeal(true)}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Add Deal
              </button>
            </div>
            <div className="border-t border-gray-200">
              {client.deals && client.deals.length > 0 ? (
                <ul className="divide-y divide-gray-200">
                  {client.deals.map((deal) => (
                    <li key={deal.id} className="px-4 py-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <CurrencyDollarIcon className="h-5 w-5 text-green-500 mr-2" />
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {deal.currency} {parseFloat(deal.amount).toLocaleString()}
                            </p>
                            <p className="text-sm text-gray-500">{deal.description}</p>
                            <p className="text-xs text-gray-400">
                              {new Date(deal.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="text-center py-6">
                  <CurrencyDollarIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No deals</h3>
                  <p className="mt-1 text-sm text-gray-500">Get started by creating a new deal.</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Communications */}
        <div>
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Communications ({communications.length})
              </h3>
              <button
                onClick={() => setShowAddCommunication(true)}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Add Communication
              </button>
            </div>
            <div className="border-t border-gray-200">
              {communications.length > 0 ? (
                <ul className="divide-y divide-gray-200">
                  {communications.map((communication) => (
                    <li key={communication.id} className="px-4 py-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="text-gray-400 mr-3">
                            {getTypeIcon(communication.type)}
                          </div>
                          <div>
                            <div className="flex items-center space-x-2">
                              <span className={getTypeBadge(communication.type)}>
                                {communication.type}
                              </span>
                              {communication.followUp && (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                  Follow Up
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-gray-900 mt-1">
                              {communication.content || 'No content'}
                            </p>
                            <p className="text-xs text-gray-400">
                              {new Date(communication.createdAt).toLocaleDateString()} at {new Date(communication.createdAt).toLocaleTimeString()}
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={() => handleDeleteCommunication(communication.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="text-center py-6">
                  <ChatBubbleLeftRightIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No communications</h3>
                  <p className="mt-1 text-sm text-gray-500">Get started by creating a new communication.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Add Deal Modal */}
      {showAddDeal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Deal</h3>
              <form onSubmit={handleAddDeal} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Amount</label>
                  <input
                    type="number"
                    step="0.01"
                    required
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={dealForm.amount}
                    onChange={(e) => setDealForm({ ...dealForm, amount: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Currency</label>
                  <select
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={dealForm.currency}
                    onChange={(e) => setDealForm({ ...dealForm, currency: e.target.value })}
                  >
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Deal Title</label>
                  <input
                    type="text"
                    required
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={dealForm.description}
                    onChange={(e) => setDealForm({ ...dealForm, description: e.target.value })}
                    placeholder="Enter deal or order title"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Deal Details & Specifications</label>
                  <textarea
                    rows={5}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={dealForm.details}
                    onChange={(e) => setDealForm({ ...dealForm, details: e.target.value })}
                    placeholder="Enter detailed information about the deal including specifications, requirements, terms, deliverables, and any other relevant details..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Notes</label>
                  <textarea
                    rows={3}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={dealForm.notes}
                    onChange={(e) => setDealForm({ ...dealForm, notes: e.target.value })}
                  />
                </div>
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddDeal(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700"
                  >
                    Add Deal
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Add Communication Modal */}
      {showAddCommunication && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Communication</h3>
              <form onSubmit={handleAddCommunication} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Type</label>
                  <select
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={communicationForm.type}
                    onChange={(e) => setCommunicationForm({ ...communicationForm, type: e.target.value as 'CALL' | 'EMAIL' | 'OTHER' })}
                  >
                    <option value="CALL">Call</option>
                    <option value="EMAIL">Email</option>
                    <option value="OTHER">Other</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Content / Notes</label>
                  <textarea
                    rows={3}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={communicationForm.content}
                    onChange={(e) => setCommunicationForm({ ...communicationForm, content: e.target.value })}
                    placeholder="Enter details about the communication..."
                  />
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    checked={communicationForm.followUp}
                    onChange={(e) => setCommunicationForm({ ...communicationForm, followUp: e.target.checked })}
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    Requires follow-up
                  </label>
                </div>
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddCommunication(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700"
                  >
                    Add Communication
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientDetail;

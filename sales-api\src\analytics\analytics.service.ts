import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class AnalyticsService {
  constructor(private prisma: PrismaService) {}

  private getDateRange(period: string = '7d') {
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case '1d':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '1m':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        break;
      case '1y':
        startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    return { startDate, endDate: now };
  }

  async getOverview() {
    const [totalUsers, totalClients, totalDeals, totalRevenue] = await Promise.all([
      this.prisma.user.count({ where: { role: 'USER' } }),
      this.prisma.client.count(),
      this.prisma.deal.count(),
      this.prisma.deal.aggregate({
        _sum: { amount: true },
      }),
    ]);

    return {
      totalSalesmen: totalUsers,
      totalClients,
      totalDeals,
      totalRevenue: totalRevenue._sum.amount || 0,
    };
  }

  async getSalesmanPerformance(period: string = '7d') {
    const { startDate, endDate } = this.getDateRange(period);

    const salesmen = await this.prisma.user.findMany({
      where: { role: 'USER' },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        deals: {
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate,
            },
          },
          select: {
            amount: true,
          },
        },
      },
    });

    // Get client counts using the proper relation
    const salesmenWithClients = await this.prisma.user.findMany({
      where: { role: 'USER' },
      select: {
        id: true,
        _count: {
          select: {
            assignedClients: {
              where: {
                createdAt: {
                  gte: startDate,
                  lte: endDate,
                },
              },
            },
          },
        },
      },
    });

    const clientCountMap = salesmenWithClients.reduce((acc, user) => {
      acc[user.id] = user._count.assignedClients;
      return acc;
    }, {} as Record<number, number>);

    return salesmen.map(salesman => ({
      id: salesman.id,
      name: salesman.firstName && salesman.lastName
        ? `${salesman.firstName} ${salesman.lastName}`
        : salesman.email,
      email: salesman.email,
      dealsCount: salesman.deals.length,
      clientsCount: clientCountMap[salesman.id] || 0,
      totalRevenue: salesman.deals.reduce((sum, deal) => sum + Number(deal.amount), 0),
    }));
  }

  async getDealsByPeriod(period: string = '7d') {
    const { startDate, endDate } = this.getDateRange(period);

    const deals = await this.prisma.deal.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      select: {
        createdAt: true,
        amount: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // Group by day
    const dailyData = deals.reduce((acc, deal) => {
      const date = deal.createdAt.toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = { count: 0, revenue: 0 };
      }
      acc[date].count += 1;
      acc[date].revenue += Number(deal.amount);
      return acc;
    }, {} as Record<string, { count: number; revenue: number }>);

    return Object.entries(dailyData).map(([date, data]) => ({
      date,
      count: data.count,
      revenue: data.revenue,
    }));
  }

  async getClientsByPeriod(period: string = '7d') {
    const { startDate, endDate } = this.getDateRange(period);

    const clients = await this.prisma.client.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      select: {
        createdAt: true,
        type: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // Group by day
    const dailyData = clients.reduce((acc, client) => {
      const date = client.createdAt.toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = { count: 0, types: {} };
      }
      acc[date].count += 1;
      acc[date].types[client.type] = (acc[date].types[client.type] || 0) + 1;
      return acc;
    }, {} as Record<string, { count: number; types: Record<string, number> }>);

    return Object.entries(dailyData).map(([date, data]) => ({
      date,
      count: data.count,
      types: data.types,
    }));
  }

  async getRevenueTrends(period: string = '7d') {
    const { startDate, endDate } = this.getDateRange(period);

    const deals = await this.prisma.deal.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      select: {
        createdAt: true,
        amount: true,
        currency: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // Group by day and currency
    const dailyRevenue = deals.reduce((acc, deal) => {
      const date = deal.createdAt.toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = {};
      }
      if (!acc[date][deal.currency]) {
        acc[date][deal.currency] = 0;
      }
      acc[date][deal.currency] += Number(deal.amount);
      return acc;
    }, {} as Record<string, Record<string, number>>);

    return Object.entries(dailyRevenue).map(([date, currencies]) => ({
      date,
      currencies,
      total: Object.values(currencies).reduce((sum, amount) => sum + amount, 0),
    }));
  }

  async getTopPerformers(period: string = '7d') {
    const { startDate, endDate } = this.getDateRange(period);

    const topByDeals = await this.prisma.user.findMany({
      where: { role: 'USER' },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        _count: {
          select: {
            deals: {
              where: {
                createdAt: {
                  gte: startDate,
                  lte: endDate,
                },
              },
            },
          },
        },
      },
      orderBy: {
        deals: {
          _count: 'desc',
        },
      },
      take: 5,
    });

    const topByRevenue = await this.prisma.user.findMany({
      where: { role: 'USER' },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        deals: {
          where: {
            createdAt: {
              gte: startDate,
              lte: endDate,
            },
          },
          select: {
            amount: true,
          },
        },
      },
    });

    const topByRevenueProcessed = topByRevenue
      .map(user => ({
        id: user.id,
        name: user.firstName && user.lastName 
          ? `${user.firstName} ${user.lastName}`
          : user.email,
        email: user.email,
        totalRevenue: user.deals.reduce((sum, deal) => sum + Number(deal.amount), 0),
      }))
      .sort((a, b) => b.totalRevenue - a.totalRevenue)
      .slice(0, 5);

    return {
      topByDeals: topByDeals.map(user => ({
        id: user.id,
        name: user.firstName && user.lastName 
          ? `${user.firstName} ${user.lastName}`
          : user.email,
        email: user.email,
        dealsCount: user._count.deals,
      })),
      topByRevenue: topByRevenueProcessed,
    };
  }

  async getActivitySummary() {
    const now = new Date();
    const periods = {
      today: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
      thisWeek: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      thisMonth: new Date(now.getFullYear(), now.getMonth(), 1),
      thisYear: new Date(now.getFullYear(), 0, 1),
    };

    const summary = {};

    for (const [periodName, startDate] of Object.entries(periods)) {
      const [dealsCount, clientsCount, revenue] = await Promise.all([
        this.prisma.deal.count({
          where: {
            createdAt: {
              gte: startDate,
            },
          },
        }),
        this.prisma.client.count({
          where: {
            createdAt: {
              gte: startDate,
            },
          },
        }),
        this.prisma.deal.aggregate({
          where: {
            createdAt: {
              gte: startDate,
            },
          },
          _sum: {
            amount: true,
          },
        }),
      ]);

      summary[periodName] = {
        deals: dealsCount,
        clients: clientsCount,
        revenue: revenue._sum.amount || 0,
      };
    }

    return summary;
  }
}

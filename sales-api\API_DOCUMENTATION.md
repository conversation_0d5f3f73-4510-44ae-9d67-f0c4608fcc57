# Sales API Documentation

## Base URL
```
http://localhost:3300
```

## Authentication
All endpoints except registration and login require a JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Response Format
All responses follow this general format:
- Success responses return the requested data directly
- Error responses return an object with `message`, `error`, and `statusCode`

---

## Authentication Endpoints

### 1. Register User
**POST** `/auth/register`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "role": "USER" // Optional: "USER" or "ADMIN", defaults to "USER"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "role": "USER"
  }
}
```

### 2. Login User
**POST** `/auth/login`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "USER"
  }
}
```

### 3. Get User Profile
**GET** `/auth/profile`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "USER"
  }
}
```

### 4. Logout User
**POST** `/auth/logout`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "message": "Logged out successfully"
}
```

---

## Client Endpoints

### 1. Create Client
**POST** `/clients`

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "name": "ABC Company",
  "type": "COMPANY", // ORGANIZATION, COMPANY, STORE, PERSON, OTHER
  "email": "<EMAIL>", // Optional
  "phoneNumbers": ["+**********", "+0987654321"], // Optional array
  "address": "123 Main St, City, State", // Optional
  "importance": "HIGH", // VERY_LOW, LOW, MEDIUM, HIGH, VERY_HIGH
  "notes": "Important client notes", // Optional
  "assignedUserId": 2 // Optional: ID of assigned salesperson
}
```

**Response:**
```json
{
  "id": 1,
  "name": "ABC Company",
  "type": "COMPANY",
  "email": "<EMAIL>",
  "phoneNumbers": ["+**********", "+0987654321"],
  "address": "123 Main St, City, State",
  "importance": "HIGH",
  "notes": "Important client notes",
  "assignedUserId": 2,
  "createdAt": "2025-07-01T19:30:00.000Z",
  "updatedAt": "2025-07-01T19:30:00.000Z",
  "assignedUser": {
    "id": 2,
    "email": "<EMAIL>",
    "firstName": "Jane",
    "lastName": "Smith"
  }
}
```

### 2. Get All Clients
**GET** `/clients`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
[
  {
    "id": 1,
    "name": "ABC Company",
    "type": "COMPANY",
    "email": "<EMAIL>",
    "phoneNumbers": ["+**********"],
    "address": "123 Main St",
    "importance": "HIGH",
    "notes": "Important client",
    "assignedUserId": 2,
    "createdAt": "2025-07-01T19:30:00.000Z",
    "updatedAt": "2025-07-01T19:30:00.000Z",
    "assignedUser": {
      "id": 2,
      "email": "<EMAIL>",
      "firstName": "Jane",
      "lastName": "Smith"
    },
    "_count": {
      "deals": 3
    }
  }
]
```

### 3. Get Client by ID
**GET** `/clients/:id`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "id": 1,
  "name": "ABC Company",
  "type": "COMPANY",
  "email": "<EMAIL>",
  "phoneNumbers": ["+**********"],
  "address": "123 Main St",
  "importance": "HIGH",
  "notes": "Important client",
  "assignedUserId": 2,
  "createdAt": "2025-07-01T19:30:00.000Z",
  "updatedAt": "2025-07-01T19:30:00.000Z",
  "assignedUser": {
    "id": 2,
    "email": "<EMAIL>",
    "firstName": "Jane",
    "lastName": "Smith"
  },
  "deals": [
    {
      "id": 1,
      "amount": "5000.00",
      "currency": "USD",
      "description": "Software license",
      "notes": "Annual subscription",
      "createdAt": "2025-07-01T19:35:00.000Z",
      "user": {
        "id": 2,
        "email": "<EMAIL>",
        "firstName": "Jane",
        "lastName": "Smith"
      }
    }
  ]
}
```

### 4. Update Client
**PATCH** `/clients/:id`

**Headers:** `Authorization: Bearer <token>`

**Request Body:** (All fields optional)
```json
{
  "name": "ABC Corporation",
  "importance": "VERY_HIGH",
  "notes": "Updated notes"
}
```

**Response:** Same as Create Client response with updated data

### 5. Delete Client
**DELETE** `/clients/:id`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "id": 1,
  "name": "ABC Company",
  // ... other client data
}
```

### 6. Get Client Deals
**GET** `/clients/:id/deals`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
[
  {
    "id": 1,
    "clientId": 1,
    "userId": 2,
    "amount": "5000.00",
    "currency": "USD",
    "description": "Software license",
    "notes": "Annual subscription",
    "createdAt": "2025-07-01T19:35:00.000Z",
    "updatedAt": "2025-07-01T19:35:00.000Z",
    "user": {
      "id": 2,
      "email": "<EMAIL>",
      "firstName": "Jane",
      "lastName": "Smith"
    }
  }
]
```

---

## Deal Endpoints

### 1. Create Deal
**POST** `/deals`

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "clientId": 1,
  "amount": 5000.50,
  "currency": "USD", // Optional, defaults to "USD"
  "description": "Software license deal",
  "details": "Comprehensive software package including CRM, analytics, and support tools", // Optional
  "notes": "Annual subscription with support" // Optional
}
```

**Response:**
```json
{
  "id": 1,
  "clientId": 1,
  "userId": 2,
  "amount": "5000.50",
  "currency": "USD",
  "description": "Software license deal",
  "details": "Comprehensive software package including CRM, analytics, and support tools",
  "notes": "Annual subscription with support",
  "createdAt": "2025-07-01T19:35:00.000Z",
  "updatedAt": "2025-07-01T19:35:00.000Z",
  "client": {
    "id": 1,
    "name": "ABC Company",
    "type": "COMPANY"
  },
  "user": {
    "id": 2,
    "email": "<EMAIL>",
    "firstName": "Jane",
    "lastName": "Smith"
  }
}
```

### 2. Get All Deals
**GET** `/deals`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
[
  {
    "id": 1,
    "clientId": 1,
    "userId": 2,
    "amount": "5000.50",
    "currency": "USD",
    "description": "Software license deal",
    "notes": "Annual subscription",
    "createdAt": "2025-07-01T19:35:00.000Z",
    "updatedAt": "2025-07-01T19:35:00.000Z",
    "client": {
      "id": 1,
      "name": "ABC Company",
      "type": "COMPANY"
    },
    "user": {
      "id": 2,
      "email": "<EMAIL>",
      "firstName": "Jane",
      "lastName": "Smith"
    }
  }
]
```

### 3. Get Deal by ID
**GET** `/deals/:id`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "id": 1,
  "clientId": 1,
  "userId": 2,
  "amount": "5000.50",
  "currency": "USD",
  "description": "Software license deal",
  "notes": "Annual subscription",
  "createdAt": "2025-07-01T19:35:00.000Z",
  "updatedAt": "2025-07-01T19:35:00.000Z",
  "client": {
    "id": 1,
    "name": "ABC Company",
    "type": "COMPANY",
    "email": "<EMAIL>",
    "phoneNumbers": ["+**********"],
    "address": "123 Main St"
  },
  "user": {
    "id": 2,
    "email": "<EMAIL>",
    "firstName": "Jane",
    "lastName": "Smith"
  }
}
```

### 4. Update Deal
**PATCH** `/deals/:id`

**Headers:** `Authorization: Bearer <token>`

**Request Body:** (All fields optional)
```json
{
  "amount": 6000.00,
  "description": "Updated software license deal",
  "notes": "Updated notes"
}
```

**Response:** Same as Create Deal response with updated data

**Note:** Users can only update their own deals unless they are an admin.

### 5. Delete Deal
**DELETE** `/deals/:id`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "id": 1,
  "clientId": 1,
  "userId": 2,
  // ... other deal data
}
```

**Note:** Users can only delete their own deals unless they are an admin.

---

## Data Types & Enums

### Client Types
- `ORGANIZATION`
- `COMPANY`
- `STORE`
- `PERSON`
- `OTHER`

### Client Importance Levels
- `VERY_LOW`
- `LOW`
- `MEDIUM`
- `HIGH`
- `VERY_HIGH`

### User Roles
- `USER` (Regular salesperson)
- `ADMIN` (Administrator with full access)

---

## Error Responses

### 400 Bad Request
```json
{
  "message": ["email must be an email", "password must be longer than or equal to 6 characters"],
  "error": "Bad Request",
  "statusCode": 400
}
```

### 401 Unauthorized
```json
{
  "message": "Unauthorized",
  "statusCode": 401
}
```

### 403 Forbidden
```json
{
  "message": "You can only update your own deals",
  "error": "Forbidden",
  "statusCode": 403
}
```

### 404 Not Found
```json
{
  "message": "Client with ID 999 not found",
  "error": "Not Found",
  "statusCode": 404
}
```

### 409 Conflict
```json
{
  "message": "User with this email already exists",
  "error": "Conflict",
  "statusCode": 409
}
```

---

## Notes for Frontend Development

1. **Authentication Flow:**
   - Store JWT token in localStorage or secure cookie
   - Include token in all API requests
   - Handle token expiration (24 hours)
   - Redirect to login on 401 responses

2. **Phone Numbers:**
   - Stored as array of strings
   - Can be empty array
   - No format validation on backend

3. **Currency:**
   - Amounts stored as decimal with 2 decimal places
   - Currency field is string (defaults to "USD")

4. **Permissions:**
   - Regular users can only edit/delete their own deals
   - Admins can edit/delete any deals
   - All authenticated users can view all clients and deals

5. **Client Assignment:**
   - Clients can be assigned to salespeople via `assignedUserId`
   - Assignment is optional (can be null)
   - No restriction on who can be assigned

6. **Data Relationships:**
   - Each deal belongs to one client and one user (salesperson)
   - Clients can have multiple deals
   - Users can have multiple deals and assigned clients

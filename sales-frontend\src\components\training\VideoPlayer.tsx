import React, { useState, useRef, useEffect } from 'react';
import { VideoProgress, videosAPI } from '../../services/api';
import './VideoPlayer.css';
import {
  XMarkIcon,
  CheckCircleIcon,
  PlayIcon,
  PauseIcon,
} from '@heroicons/react/24/outline';

interface VideoPlayerProps {
  videoProgress: VideoProgress;
  onComplete: (videoId: number) => void;
  onClose: () => void;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ videoProgress, onComplete, onClose }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showCompleteButton, setShowCompleteButton] = useState(false);
  const [maxWatchedTime, setMaxWatchedTime] = useState(0);

  const video = videoProgress.video;

  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const handleTimeUpdate = () => {
      const currentVideoTime = videoElement.currentTime;

      // Prevent seeking backwards - only allow forward progress
      if (currentVideoTime > maxWatchedTime) {
        setMaxWatchedTime(currentVideoTime);
        setCurrentTime(currentVideoTime);
      } else if (currentVideoTime < maxWatchedTime - 1) {
        // User tried to seek backwards, reset to max watched time
        videoElement.currentTime = maxWatchedTime;
        return;
      } else {
        setCurrentTime(currentVideoTime);
      }

      // Show complete button when user has watched 95% of the video continuously
      const watchedPercentage = (maxWatchedTime / videoElement.duration) * 100;
      if (watchedPercentage >= 95 && !videoProgress.isCompleted) {
        setShowCompleteButton(true);
      }
    };

    const handleLoadedMetadata = () => {
      setDuration(videoElement.duration);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      // User watched to the end, allow completion
      if (!videoProgress.isCompleted) {
        setShowCompleteButton(true);
      }
    };

    videoElement.addEventListener('timeupdate', handleTimeUpdate);
    videoElement.addEventListener('loadedmetadata', handleLoadedMetadata);
    videoElement.addEventListener('ended', handleEnded);

    return () => {
      videoElement.removeEventListener('timeupdate', handleTimeUpdate);
      videoElement.removeEventListener('loadedmetadata', handleLoadedMetadata);
      videoElement.removeEventListener('ended', handleEnded);
    };
  }, [videoProgress.isCompleted]);

  const togglePlayPause = () => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    if (isPlaying) {
      videoElement.pause();
    } else {
      videoElement.play();
    }
    setIsPlaying(!isPlaying);
  };



  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleMarkComplete = async () => {
    if (!video) return;

    try {
      await onComplete(video.id);
      setShowCompleteButton(false);
    } catch (error) {
      console.error('Failed to mark video as complete:', error);
    }
  };

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  // Return null if video is not available (after all hooks have been called)
  if (!video) {
    return null;
  }

  // Debug: Log video info
  console.log('Video info:', video);
  console.log('Video stream URL:', videosAPI.getStreamUrl(video.id));

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <div>
            <h3 className="text-lg font-medium text-gray-900">{video.title}</h3>
            {video.description && (
              <p className="text-sm text-gray-500 mt-1">{video.description}</p>
            )}
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Video Player */}
        <div className="relative bg-black">
          <video
            ref={videoRef}
            className="w-full h-auto max-h-[60vh]"
            src={videosAPI.getStreamUrl(video.id)}
            preload="none"
            playsInline
            crossOrigin="anonymous"
            poster="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzMzMzMzMyIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+TG9hZGluZy4uLjwvdGV4dD48L3N2Zz4="
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            onError={(e) => {
              const error = e.currentTarget.error;
              console.error('Video playback error:', error?.code, error?.message);
            }}
            onLoadedMetadata={() => {
              console.log('Video metadata loaded');
            }}
            onCanPlay={() => {
              console.log('Video ready to play');
            }}
            onWaiting={() => {
              console.log('Video buffering...');
            }}
            controls={false}
          >
            Your browser does not support the video tag.
          </video>

          {/* Custom Controls Overlay */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
            {/* Progress Bar - Non-interactive */}
            <div className="mb-4">
              <div className="w-full bg-gray-600 rounded-lg h-2">
                <div
                  className="bg-blue-500 h-2 rounded-lg transition-all duration-300"
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-xs text-gray-300 mt-1">
                <span>Progress: {progressPercentage.toFixed(1)}%</span>
                <span>No seeking allowed</span>
              </div>
            </div>

            {/* Controls */}
            <div className="flex items-center justify-between text-white">
              <div className="flex items-center space-x-4">
                <button
                  onClick={togglePlayPause}
                  className="p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-all"
                >
                  {isPlaying ? (
                    <PauseIcon className="h-6 w-6" />
                  ) : (
                    <PlayIcon className="h-6 w-6" />
                  )}
                </button>
                <span className="text-sm">
                  {formatTime(currentTime)} / {formatTime(duration)}
                </span>
              </div>

              {videoProgress.isCompleted && (
                <div className="flex items-center space-x-2 text-green-400">
                  <CheckCircleIcon className="h-5 w-5" />
                  <span className="text-sm">Completed</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              {video.isRequired && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                  Required
                </span>
              )}
              {videoProgress.isCompleted ? (
                <span className="text-green-600">
                  ✓ Completed on {videoProgress.watchedAt && new Date(videoProgress.watchedAt).toLocaleDateString()}
                </span>
              ) : (
                <div>
                  <div className="text-orange-600 font-medium">⚠️ Watch the entire video to mark as complete</div>
                  <div className="text-xs text-gray-500 mt-1">Seeking is disabled - you must watch from start to finish</div>
                </div>
              )}
            </div>

            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Close
              </button>
              
              {showCompleteButton && !videoProgress.isCompleted && (
                <button
                  onClick={handleMarkComplete}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <CheckCircleIcon className="h-4 w-4 mr-2 inline" />
                  Mark as Complete
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;

import { PrismaClient, Role } from '@prisma/client';

const prisma = new PrismaClient();

async function seedTraining() {
  try {
    console.log('🌱 Seeding training data...');

    // Set all admin users as training completed
    const adminUpdateResult = await prisma.user.updateMany({
      where: {
        role: Role.ADMIN,
      },
      data: {
        trainingCompleted: true,
      },
    });

    console.log(`✅ Updated ${adminUpdateResult.count} admin users to have training completed`);

    // Create some sample training videos (optional - for demo purposes)
    const sampleVideos = [
      {
        title: 'Welcome to Sales Platform',
        description: 'Introduction to the sales platform and its features',
        filename: 'welcome-video.mp4',
        filePath: './uploads/videos/welcome-video.mp4',
        duration: 300, // 5 minutes
        order: 1,
        isRequired: true,
        isActive: true,
      },
      {
        title: 'Client Management Basics',
        description: 'Learn how to manage clients effectively',
        filename: 'client-management.mp4',
        filePath: './uploads/videos/client-management.mp4',
        duration: 450, // 7.5 minutes
        order: 2,
        isRequired: true,
        isActive: true,
      },
      {
        title: 'Deal Tracking and Management',
        description: 'Master the art of tracking and managing deals',
        filename: 'deal-management.mp4',
        filePath: './uploads/videos/deal-management.mp4',
        duration: 600, // 10 minutes
        order: 3,
        isRequired: true,
        isActive: true,
      },
      {
        title: 'Communication Best Practices',
        description: 'Learn effective communication strategies with clients',
        filename: 'communication-best-practices.mp4',
        filePath: './uploads/videos/communication-best-practices.mp4',
        duration: 360, // 6 minutes
        order: 4,
        isRequired: true,
        isActive: true,
      },
    ];

    // Check if videos already exist to avoid duplicates
    const existingVideos = await prisma.trainingVideo.count();
    
    if (existingVideos === 0) {
      console.log('📹 Creating sample training videos...');
      
      for (const video of sampleVideos) {
        await prisma.trainingVideo.create({
          data: video,
        });
        console.log(`✅ Created video: ${video.title}`);
      }
    } else {
      console.log('📹 Training videos already exist, skipping creation');
    }

    console.log('🎉 Training data seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding training data:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedTraining()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export default seedTraining;

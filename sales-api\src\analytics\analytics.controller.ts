import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '@prisma/client';

@Controller('analytics')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.ADMIN)
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('overview')
  async getOverview() {
    return this.analyticsService.getOverview();
  }

  @Get('salesman-performance')
  async getSalesmanPerformance(@Query('period') period?: string) {
    return this.analyticsService.getSalesmanPerformance(period);
  }

  @Get('deals-by-period')
  async getDealsByPeriod(@Query('period') period?: string) {
    return this.analyticsService.getDealsByPeriod(period);
  }

  @Get('clients-by-period')
  async getClientsByPeriod(@Query('period') period?: string) {
    return this.analyticsService.getClientsByPeriod(period);
  }

  @Get('revenue-trends')
  async getRevenueTrends(@Query('period') period?: string) {
    return this.analyticsService.getRevenueTrends(period);
  }

  @Get('top-performers')
  async getTopPerformers(@Query('period') period?: string) {
    return this.analyticsService.getTopPerformers(period);
  }

  @Get('activity-summary')
  async getActivitySummary() {
    return this.analyticsService.getActivitySummary();
  }
}

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Communication, communicationsAPI, clientsAPI, Client } from '../../services/api';

interface CommunicationFormProps {
  isEdit?: boolean;
}

const CommunicationForm: React.FC<CommunicationFormProps> = ({ isEdit = false }) => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    type: 'CALL' as 'CALL' | 'EMAIL' | 'OTHER',
    content: '',
    followUp: false,
    clientId: '',
  });

  useEffect(() => {
    fetchClients();
    if (isEdit && id) {
      fetchCommunication();
    }
  }, [isEdit, id]);

  const fetchClients = async () => {
    try {
      const data = await clientsAPI.getAll();
      setClients(data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch clients');
    }
  };

  const fetchCommunication = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const data = await communicationsAPI.getById(parseInt(id));
      setFormData({
        type: data.type,
        content: data.content || '',
        followUp: data.followUp,
        clientId: data.clientId.toString(),
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch communication');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.clientId) {
      setError('Please select a client');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const communicationData = {
        type: formData.type,
        content: formData.content || undefined,
        followUp: formData.followUp,
        clientId: parseInt(formData.clientId),
      };

      if (isEdit && id) {
        await communicationsAPI.update(parseInt(id), communicationData);
      } else {
        await communicationsAPI.create(communicationData);
      }

      navigate('/communications');
    } catch (err: any) {
      setError(err.response?.data?.message || `Failed to ${isEdit ? 'update' : 'create'} communication`);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  if (loading && isEdit) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            {isEdit ? 'Edit Communication' : 'New Communication'}
          </h2>
        </div>
      </div>

      <div className="mt-8">
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="space-y-6 p-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="text-sm text-red-600">{error}</div>
              </div>
            )}

            <div>
              <label htmlFor="clientId" className="block text-sm font-medium text-gray-700">
                Client *
              </label>
              <select
                id="clientId"
                name="clientId"
                value={formData.clientId}
                onChange={handleChange}
                required
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select a client</option>
                {clients.map((client) => (
                  <option key={client.id} value={client.id}>
                    {client.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                Communication Type *
              </label>
              <select
                id="type"
                name="type"
                value={formData.type}
                onChange={handleChange}
                required
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="CALL">Call</option>
                <option value="EMAIL">Email</option>
                <option value="OTHER">Other</option>
              </select>
            </div>

            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700">
                Content / Notes
              </label>
              <textarea
                id="content"
                name="content"
                rows={4}
                value={formData.content}
                onChange={handleChange}
                placeholder="Enter details about the communication, outcomes, or any relevant notes..."
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
              <p className="mt-2 text-sm text-gray-500">
                Optional: Add details about what was discussed or the outcome of the communication.
              </p>
            </div>

            <div className="flex items-center">
              <input
                id="followUp"
                name="followUp"
                type="checkbox"
                checked={formData.followUp}
                onChange={handleChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="followUp" className="ml-2 block text-sm text-gray-900">
                Requires follow-up
              </label>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => navigate('/communications')}
                className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {loading ? 'Saving...' : (isEdit ? 'Update Communication' : 'Create Communication')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CommunicationForm;

import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCommunicationDto } from './dto/create-communication.dto';
import { UpdateCommunicationDto } from './dto/update-communication.dto';
import { User, Role } from '@prisma/client';

@Injectable()
export class CommunicationsService {
  constructor(private prisma: PrismaService) {}

  async create(createCommunicationDto: CreateCommunicationDto, user: User) {
    // Verify user has access to the client
    if (user.role !== Role.ADMIN) {
      const client = await this.prisma.client.findFirst({
        where: {
          id: createCommunicationDto.clientId,
          OR: [
            { assignedUserId: user.id },
            { createdById: user.id }
          ]
        }
      });

      if (!client) {
        throw new ForbiddenException('You do not have access to this client');
      }
    }

    return this.prisma.communication.create({
      data: {
        ...createCommunicationDto,
        userId: user.id,
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        client: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }

  async findAll(user: User) {
    const where = user.role === Role.ADMIN 
      ? {} 
      : { userId: user.id };

    return this.prisma.communication.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        client: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: number, user: User) {
    const communication = await this.prisma.communication.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        client: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!communication) {
      throw new NotFoundException('Communication not found');
    }

    // Users can only access their own communications unless they're admin
    if (user.role !== Role.ADMIN && communication.userId !== user.id) {
      throw new ForbiddenException('You can only access your own communications');
    }

    return communication;
  }

  async update(id: number, updateCommunicationDto: UpdateCommunicationDto, user: User) {
    const communication = await this.findOne(id, user);

    // Users can only update their own communications
    if (user.role !== Role.ADMIN && communication.userId !== user.id) {
      throw new ForbiddenException('You can only update your own communications');
    }

    return this.prisma.communication.update({
      where: { id },
      data: updateCommunicationDto,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        client: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }

  async remove(id: number, user: User) {
    const communication = await this.findOne(id, user);

    // Users can only delete their own communications
    if (user.role !== Role.ADMIN && communication.userId !== user.id) {
      throw new ForbiddenException('You can only delete your own communications');
    }

    return this.prisma.communication.delete({
      where: { id },
    });
  }

  async findByClient(clientId: number, user: User) {
    // Verify user has access to the client
    if (user.role !== Role.ADMIN) {
      const client = await this.prisma.client.findFirst({
        where: {
          id: clientId,
          OR: [
            { assignedUserId: user.id },
            { createdById: user.id }
          ]
        }
      });

      if (!client) {
        throw new ForbiddenException('You do not have access to this client');
      }
    }

    const where = user.role === Role.ADMIN 
      ? { clientId } 
      : { clientId, userId: user.id };

    return this.prisma.communication.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        client: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  // Admin-only method to get all communications with filtering
  async findAllForAdmin(filters?: {
    userId?: number;
    clientId?: number;
    date?: string;
    type?: string;
  }) {
    const where: any = {};

    if (filters?.userId) {
      where.userId = filters.userId;
    }

    if (filters?.clientId) {
      where.clientId = filters.clientId;
    }

    if (filters?.type) {
      where.type = filters.type;
    }

    if (filters?.date) {
      const startDate = new Date(filters.date);
      const endDate = new Date(filters.date);
      endDate.setDate(endDate.getDate() + 1);

      where.createdAt = {
        gte: startDate,
        lt: endDate,
      };
    }

    return this.prisma.communication.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        client: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
}

import React, { useState, useEffect } from 'react';
import { Client, User, clientsAPI, usersAPI } from '../../services/api';
import {
  UserPlusIcon,
  UserMinusIcon,
  MagnifyingGlassIcon,
  CheckIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

const ClientAssignment: React.FC = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [assignmentLoading, setAssignmentLoading] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [clientsData, usersData] = await Promise.all([
        clientsAPI.getAll(),
        usersAPI.getAll(),
      ]);
      setClients(clientsData);
      setUsers(usersData.filter(user => user.role === 'USER')); // Only salesmen
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch data');
    } finally {
      setLoading(false);
    }
  };

  const handleAssignClient = async (clientId: number, userId: number) => {
    try {
      setAssignmentLoading(true);
      const updatedClient = await clientsAPI.assignClient(clientId, userId);
      
      // Update the clients list
      setClients(clients.map(client => 
        client.id === clientId ? updatedClient : client
      ));
      
      setSelectedClient(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to assign client');
    } finally {
      setAssignmentLoading(false);
    }
  };

  const handleUnassignClient = async (clientId: number) => {
    try {
      setAssignmentLoading(true);
      const updatedClient = await clientsAPI.unassignClient(clientId);
      
      // Update the clients list
      setClients(clients.map(client => 
        client.id === clientId ? updatedClient : client
      ));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to unassign client');
    } finally {
      setAssignmentLoading(false);
    }
  };

  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (client.assignedUser?.firstName && 
     `${client.assignedUser.firstName} ${client.assignedUser.lastName}`.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (client.assignedUser?.email && client.assignedUser.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (loading) {
    return (
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Client Assignment</h1>
          <p className="mt-2 text-sm text-gray-700">
            Assign clients to salesmen or manage existing assignments.
          </p>
        </div>
      </div>

      {error && (
        <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {/* Search */}
      <div className="mt-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            placeholder="Search clients by name, type, or assigned salesman..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Clients Table */}
      <div className="mt-8 flex flex-col">
        <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Client
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created By
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Currently Assigned To
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Deals
                    </th>
                    <th className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredClients.map((client) => (
                    <tr key={client.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {client.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {client.type} • {client.email || 'No email'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {client.createdBy 
                            ? `${client.createdBy.firstName || ''} ${client.createdBy.lastName || ''}`.trim() || client.createdBy.email
                            : 'Unknown'
                          }
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {client.assignedUser 
                            ? `${client.assignedUser.firstName || ''} ${client.assignedUser.lastName || ''}`.trim() || client.assignedUser.email
                            : (
                              <span className="text-gray-500 italic">Unassigned</span>
                            )
                          }
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {client._count?.deals || 0}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          {client.assignedUserId ? (
                            <button
                              onClick={() => handleUnassignClient(client.id)}
                              disabled={assignmentLoading}
                              className="text-red-600 hover:text-red-900 disabled:opacity-50"
                              title="Unassign"
                            >
                              <UserMinusIcon className="h-5 w-5" />
                            </button>
                          ) : null}
                          <button
                            onClick={() => setSelectedClient(client)}
                            disabled={assignmentLoading}
                            className="text-primary-600 hover:text-primary-900 disabled:opacity-50"
                            title="Assign to salesman"
                          >
                            <UserPlusIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Assignment Modal */}
      {selectedClient && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Assign Client: {selectedClient.name}
                </h3>
                <button
                  onClick={() => setSelectedClient(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
              
              <div className="space-y-3">
                <p className="text-sm text-gray-600">
                  Select a salesman to assign this client to:
                </p>
                
                {users.map((user) => (
                  <button
                    key={user.id}
                    onClick={() => handleAssignClient(selectedClient.id, user.id)}
                    disabled={assignmentLoading}
                    className="w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 disabled:opacity-50 flex items-center justify-between"
                  >
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {user.firstName && user.lastName 
                          ? `${user.firstName} ${user.lastName}`
                          : user.email
                        }
                      </div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                    </div>
                    {selectedClient.assignedUserId === user.id && (
                      <CheckIcon className="h-5 w-5 text-green-500" />
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientAssignment;

import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateVideoDto } from './dto/create-video.dto';
import { UpdateVideoDto } from './dto/update-video.dto';
import { UpdateVideoProgressDto } from './dto/video-progress.dto';
import { User, Role } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class VideosService {
  constructor(private prisma: PrismaService) {}

  /**
   * Helper method to resolve video file paths for Docker/container compatibility
   */
  private resolveVideoPath(storedPath: string, filename: string): string {
    const possiblePaths = [
      storedPath,
      path.resolve(storedPath),
      path.join(process.cwd(), storedPath),
      path.join('./uploads/videos', filename),
      path.join(process.cwd(), 'uploads/videos', filename),
    ];

    for (const testPath of possiblePaths) {
      if (fs.existsSync(testPath)) {
        return testPath;
      }
    }

    throw new NotFoundException(`Video file not found: ${filename}`);
  }

  async create(createVideoDto: CreateVideoDto, filename: string, filePath: string) {
    return this.prisma.trainingVideo.create({
      data: {
        ...createVideoDto,
        filename,
        filePath,
      },
    });
  }

  async findAll() {
    return this.prisma.trainingVideo.findMany({
      where: { isActive: true },
      orderBy: { order: 'asc' },
    });
  }

  async findAllForAdmin() {
    return this.prisma.trainingVideo.findMany({
      orderBy: { order: 'asc' },
      include: {
        userProgress: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
    });
  }

  async findOne(id: number) {
    console.log(`🔍 Looking for video with ID: ${id}`);

    const video = await this.prisma.trainingVideo.findUnique({
      where: { id },
    });

    if (!video) {
      console.error(`❌ Video not found with ID: ${id}`);

      // List all videos to see what's available
      const allVideos = await this.prisma.trainingVideo.findMany({
        select: { id: true, title: true, filename: true, isActive: true }
      });
      console.log(`📋 Available videos:`, allVideos);

      throw new NotFoundException(`Video not found with ID: ${id}`);
    }

    console.log(`✅ Video found:`, {
      id: video.id,
      title: video.title,
      filename: video.filename,
      filePath: video.filePath,
      isActive: video.isActive
    });

    return video;
  }

  async getVideoWithResolvedPath(id: number) {
    const video = await this.findOne(id);
    const resolvedPath = this.resolveVideoPath(video.filePath, video.filename);

    return {
      ...video,
      resolvedFilePath: resolvedPath,
    };
  }

  async update(id: number, updateVideoDto: UpdateVideoDto) {
    const video = await this.findOne(id);

    return this.prisma.trainingVideo.update({
      where: { id },
      data: updateVideoDto,
    });
  }

  async remove(id: number) {
    const video = await this.findOne(id);

    // Delete the physical file
    if (fs.existsSync(video.filePath)) {
      fs.unlinkSync(video.filePath);
    }

    return this.prisma.trainingVideo.delete({
      where: { id },
    });
  }

  // Video Progress Methods
  async getUserProgress(userId: number) {
    // Get all active videos
    const videos = await this.prisma.trainingVideo.findMany({
      where: { isActive: true },
      orderBy: { order: 'asc' },
    });

    // Get existing progress for this user
    const existingProgress = await this.prisma.userVideoProgress.findMany({
      where: { userId },
      include: {
        video: true,
      },
    });

    // Create a map of existing progress by videoId
    const progressMap = new Map(
      existingProgress.map(progress => [progress.videoId, progress])
    );

    // Return all videos with their progress status
    return videos.map(video => {
      const progress = progressMap.get(video.id);
      return {
        id: progress?.id || 0, // Use 0 for new progress records
        userId,
        videoId: video.id,
        isCompleted: progress?.isCompleted || false,
        watchedAt: progress?.watchedAt || null,
        createdAt: progress?.createdAt || new Date(),
        updatedAt: progress?.updatedAt || new Date(),
        video: {
          id: video.id,
          title: video.title,
          description: video.description,
          duration: video.duration,
          order: video.order,
          isRequired: video.isRequired,
        },
      };
    });
  }

  async updateProgress(userId: number, videoId: number, updateProgressDto: UpdateVideoProgressDto) {
    // Verify video exists
    await this.findOne(videoId);

    const progressData = {
      userId,
      videoId,
      isCompleted: updateProgressDto.isCompleted,
      watchedAt: updateProgressDto.isCompleted ? new Date() : null,
    };

    return this.prisma.userVideoProgress.upsert({
      where: {
        userId_videoId: { userId, videoId },
      },
      update: {
        isCompleted: progressData.isCompleted,
        watchedAt: progressData.watchedAt,
      },
      create: progressData,
      include: {
        video: {
          select: {
            id: true,
            title: true,
            description: true,
            duration: true,
            order: true,
            isRequired: true,
          },
        },
      },
    });
  }

  async checkUserTrainingCompletion(userId: number): Promise<boolean> {
    const requiredVideos = await this.prisma.trainingVideo.findMany({
      where: { isRequired: true, isActive: true },
      select: { id: true },
    });

    const completedVideos = await this.prisma.userVideoProgress.findMany({
      where: {
        userId,
        isCompleted: true,
        videoId: { in: requiredVideos.map(v => v.id) },
      },
      select: { videoId: true },
    });

    const isCompleted = completedVideos.length === requiredVideos.length;

    // Update user's training completion status
    await this.prisma.user.update({
      where: { id: userId },
      data: { trainingCompleted: isCompleted },
    });

    return isCompleted;
  }

  async getTrainingStats() {
    const totalVideos = await this.prisma.trainingVideo.count({
      where: { isRequired: true, isActive: true },
    });

    const totalUsers = await this.prisma.user.count({
      where: { role: Role.USER, isActive: true },
    });

    const completedUsers = await this.prisma.user.count({
      where: { role: Role.USER, isActive: true, trainingCompleted: true },
    });

    const userProgress = await this.prisma.user.findMany({
      where: { role: Role.USER, isActive: true },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        trainingCompleted: true,
        videoProgress: {
          where: { isCompleted: true },
          include: {
            video: {
              select: { id: true, title: true, isRequired: true },
            },
          },
        },
      },
    });

    return {
      totalVideos,
      totalUsers,
      completedUsers,
      completionRate: totalUsers > 0 ? (completedUsers / totalUsers) * 100 : 0,
      userProgress: userProgress.map(user => ({
        ...user,
        completedVideos: user.videoProgress.length,
        completedRequiredVideos: user.videoProgress.filter(p => p.video.isRequired).length,
      })),
    };
  }
}
